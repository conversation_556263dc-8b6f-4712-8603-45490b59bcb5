import {
  formatDateToFirebaseTimestamp,
  OrderEntity,
  OrderStatus,
} from "../../marketplace-shared";
import { validateUserExists } from "../../services/auth-service/auth.validator";
import { validateGiftForLinking } from "../../services/gift-service/gift-validator";
import { LinkGiftToOrderLogger } from "./link-gift-to-order.logger";
import { validateLinkGiftToOrder } from "./link-gift-to-order.validator";

import {
  db,
  DBOrdersCollection,
  getCurrentDateFirestoreTimestamp,
} from "../../services/db.service";

import * as admin from "firebase-admin";
import { bpsToDecimal, safeMultiply } from "../../utils";
import {
  sendNotificationSafely,
  notifyBuyerGiftSent,
} from "../../services/notification-service";
import { transferNetAmountToSeller } from "../../services/purchase-service/purchase-fee-processing-service";

/**
 * Reusable function to process paid orders when a gift is attached
 * This handles:
 * 1. Updating order status to GIFT_SENT_TO_RELAYER
 * 2. Attaching gift ID to order
 * 3. Calculating and transferring net amount to seller
 * 4. Committing the batch (if provided) or creating a new one
 */
export async function processPaidOrderWithGift({
  order,
  giftId,
  orderDoc,
  batch: providedBatch,
  additionalOrderUpdates = {},
}: {
  order: OrderEntity;
  giftId: string;
  orderDoc: admin.firestore.DocumentSnapshot;
  batch?: admin.firestore.WriteBatch;
  additionalOrderUpdates?: Record<string, any>;
}) {
  const batch = providedBatch || db.batch();
  const shouldCommit = !providedBatch;

  const orderUpdates = {
    giftId,
    status: OrderStatus.GIFT_SENT_TO_RELAYER,
    updatedAt: formatDateToFirebaseTimestamp(
      getCurrentDateFirestoreTimestamp()
    ),
    ...additionalOrderUpdates,
  };

  batch.update(orderDoc.ref, orderUpdates);

  // Transfer funds from buyer's locked balance to seller
  if (order.buyerId && order.sellerId) {
    // Calculate net amount using order's fee structure
    const purchaseFeeRate = order.fees?.purchase_fee || 0;
    const netSellerAmount = safeMultiply(
      order.price,
      1 - bpsToDecimal(purchaseFeeRate)
    );

    // Use reusable function for money transfer
    await transferNetAmountToSeller(order, netSellerAmount, order.buyerId);
  }

  // Commit batch if we created it
  if (shouldCommit) {
    await batch.commit();
  }

  // Send notification to buyer when gift is sent to relayer
  if (order.buyerId) {
    sendNotificationSafely(
      notifyBuyerGiftSent,
      {
        orderId: order.id!,
        buyerId: order.buyerId,
        orderNumber: order.number,
      },
      "order-gift-processing-service"
    );
  }
}

export async function linkGiftToOrderService({
  giftId,
  orderId,
  userId,
}: {
  giftId: string;
  orderId: string;
  userId: string;
}) {
  // Parallelize gift validation, order fetching, and user validation
  const [gift, orderDoc, user] = await Promise.all([
    validateGiftForLinking(giftId),
    DBOrdersCollection.doc(orderId).get(),
    validateUserExists(userId),
  ]);

  LinkGiftToOrderLogger.logGiftValidation({
    giftId,
    giftOwnerId: gift.owner_tg_id,
    giftCollectionId: gift.collectionId,
  });

  // Comprehensive validation using validator functions
  const order = validateLinkGiftToOrder({
    orderDoc,
    gift,
    user,
    userId,
  });

  LinkGiftToOrderLogger.logOrderValidation({
    orderId,
    orderSellerId: order.sellerId!,
    orderStatus: order.status,
    orderCollectionId: order.collectionId,
    userId,
  });

  // 7. Link gift to order based on order status
  const batch = db.batch();

  if (order.status === OrderStatus.CREATED) {
    // For CREATED orders, activate the order
    batch.update(orderDoc.ref, {
      giftId,
      status: OrderStatus.ACTIVE,
      updatedAt: formatDateToFirebaseTimestamp(
        getCurrentDateFirestoreTimestamp()
      ),
    });

    await batch.commit();

    LinkGiftToOrderLogger.logGiftLinkedToOrder({
      giftId,
      orderId,
      userId,
      newOrderStatus: OrderStatus.ACTIVE,
    });

    return {
      success: true,
      message: "Gift linked to order and order activated successfully.",
      order: {
        ...order,
        giftId,
        status: OrderStatus.ACTIVE,
      },
    };
  } else {
    // For PAID orders, use reusable function to process paid order with gift
    await processPaidOrderWithGift({
      order,
      giftId,
      orderDoc,
      batch,
    });

    LinkGiftToOrderLogger.logGiftLinkedToOrder({
      giftId,
      orderId,
      userId,
      newOrderStatus: OrderStatus.GIFT_SENT_TO_RELAYER,
    });

    return {
      success: true,
      message: "Gift linked to paid order and sent to relayer successfully.",
      order: {
        ...order,
        giftId,
        status: OrderStatus.GIFT_SENT_TO_RELAYER,
      },
    };
  }
}
