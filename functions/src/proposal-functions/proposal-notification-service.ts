import fetch from "node-fetch";
import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";
import { validateBotConfiguration } from "../services/request.validator";

const BOT_APP_URL = process.env.BOT_APP_URL;
const AUTH_TOKEN = process.env.AUTH_TOKEN;

export interface NotifySellerNewProposalParams {
  orderId: string;
  sellerId: string;
  proposedPrice: number;
  originalPrice: number;
  orderNumber?: number;
}

export interface NotifyProposerAcceptedParams {
  orderId: string;
  proposerId: string;
  proposedPrice: number;
  orderNumber?: number;
}

// Validation logic moved to services/request.validator.ts
export async function notifySellerNewProposal(
  params: NotifySellerNewProposalParams
) {
  const { orderId, sellerId, proposedPrice, originalPrice, orderNumber } =
    params;

  // Use centralized configuration validation
  const configValidation = validateBotConfiguration(BOT_APP_URL, AUTH_TOKEN);
  if (!configValidation.isConfigured) {
    logger.warn(
      "Bot notification skipped: BOT_APP_URL or AUTH_TOKEN not configured",
      LogOperations.PROPOSAL_OPERATION,
      { orderId, sellerId }
    );
    return;
  }

  try {
    const response = await fetch(`${BOT_APP_URL}/notify-new-proposal`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify({
        orderId,
        sellerId,
        proposedPrice,
        originalPrice,
        orderNumber,
      }),
    });

    if (!response.ok) {
      logger.error(
        `Bot notification failed: ${response.status} ${response.statusText}`,
        new Error(`HTTP ${response.status}: ${response.statusText}`),
        LogOperations.PROPOSAL_OPERATION,
        { orderId, sellerId, proposedPrice, originalPrice, orderNumber }
      );
    } else {
      logger.info(
        "Successfully notified seller of new proposal",
        LogOperations.PROPOSAL_OPERATION,
        { orderId, sellerId, proposedPrice, originalPrice, orderNumber }
      );
    }
  } catch (error) {
    logger.error(
      "Error sending bot notification",
      error,
      LogOperations.PROPOSAL_OPERATION,
      { orderId, sellerId, proposedPrice, originalPrice, orderNumber }
    );
  }
}

export async function notifyProposerAccepted(
  params: NotifyProposerAcceptedParams
) {
  const { orderId, proposerId, proposedPrice, orderNumber } = params;

  if (!BOT_APP_URL || !AUTH_TOKEN) {
    logger.warn(
      "Bot notification skipped: BOT_APP_URL or AUTH_TOKEN not configured",
      LogOperations.PROPOSAL_OPERATION,
      { orderId, proposerId }
    );
    return;
  }

  try {
    const response = await fetch(`${BOT_APP_URL}/notify-proposal-accepted`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify({
        orderId,
        proposerId,
        proposedPrice,
        orderNumber,
      }),
    });

    if (!response.ok) {
      logger.error(
        `Bot notification failed: ${response.status} ${response.statusText}`,
        new Error(`HTTP ${response.status}: ${response.statusText}`),
        LogOperations.PROPOSAL_OPERATION,
        { orderId, proposerId, proposedPrice, orderNumber }
      );
    } else {
      logger.info(
        "Successfully notified proposer of accepted proposal",
        LogOperations.PROPOSAL_OPERATION,
        { orderId, proposerId, proposedPrice, orderNumber }
      );
    }
  } catch (error) {
    logger.error(
      "Error sending bot notification",
      error,
      LogOperations.PROPOSAL_OPERATION,
      { orderId, proposerId, proposedPrice, orderNumber }
    );
  }
}
