import { UserEntity, TxType } from "../marketplace-shared";
import { TRANSACTION_DESCRIPTION_INTL_KEYS } from "../transaction-description-intl-keys";
import { getUserData } from "../services/auth-service/auth.service";
import { requireTonWallet } from "../services/auth-service/auth.validator";
import { validatePositiveAmount } from "../services/financial.validator";
import {
  hasAvailableBalance,
  spendFundsWithHistory,
} from "../services/balance-service/balance-service";
import {
  applyWithdrawFee,
  getAppConfig,
} from "../services/fee-service/fee-service";
import { sendWithdrawal } from "../services/ton-wallet-service/ton-wallet-service";
import {
  checkWithdrawalLimit,
  updateWithdrawalTracking,
} from "../services/withdrawal-limit-service";
import { safeSubtract } from "../utils";
import {
  throwAmountBelowMinimum,
  throwAmountExceeds24hLimit,
  throwInsufficientBalance,
  throwAmountTooSmallAfterFees,
} from "./withdraw-function.validator";

export interface WithdrawFundsParams {
  amount: number;
}

export interface WithdrawFundsResult {
  success: boolean;
  message: string;
  netAmount: number;
  feeAmount: number;
  transactionHash?: string;
}

export async function validateWithdrawalRequest(
  userId: string,
  amount: number
): Promise<{ user: UserEntity; config: any }> {
  validatePositiveAmount(amount);

  // Parallelize user data and app config fetching
  const [user, config] = await Promise.all([
    getUserData(userId),
    getAppConfig(),
  ]);

  requireTonWallet(user);

  return { user, config };
}

export async function validateWithdrawalLimits(
  userId: string,
  amount: number,
  config?: any
): Promise<void> {
  const appConfig = config || (await getAppConfig());
  if (!appConfig) return;

  const minWithdrawal = appConfig.min_withdrawal_amount || 0;
  const maxWithdrawal24h =
    appConfig.max_withdrawal_amount || Number.MAX_SAFE_INTEGER;

  if (amount < minWithdrawal) {
    throwAmountBelowMinimum(minWithdrawal);
  }

  // Check 24-hour withdrawal limit
  const withdrawalLimitInfo = await checkWithdrawalLimit({
    userId,
    requestedAmount: amount,
    maxWithdrawalAmount: maxWithdrawal24h,
  });

  if (!withdrawalLimitInfo.canWithdraw) {
    throwAmountExceeds24hLimit(amount, withdrawalLimitInfo);
  }
}

export async function validateUserBalance(
  userId: string,
  amount: number
): Promise<void> {
  const hasBalance = await hasAvailableBalance(userId, amount);
  if (!hasBalance) {
    throwInsufficientBalance();
  }
}

export async function calculateWithdrawalAmounts(
  userId: string,
  amount: number
): Promise<{ feeAmount: number; netAmountToUser: number }> {
  const feeAmount = await applyWithdrawFee(userId, amount);
  const netAmountToUser = safeSubtract(amount, feeAmount);

  if (netAmountToUser <= 0) {
    throwAmountTooSmallAfterFees();
  }

  return { feeAmount, netAmountToUser };
}

export async function processWithdrawal(
  userId: string,
  amount: number,
  netAmountToUser: number,
  feeAmount: number,
  user: UserEntity
): Promise<{ success: boolean; transactionHash?: string }> {
  // Parallelize spending funds and sending withdrawal
  const [, transferResult] = await Promise.all([
    spendFundsWithHistory({
      userId,
      amount,
      txType: TxType.WITHDRAW,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.WITHDRAWAL_TO_TON_WALLET,
      descriptionIntlParams: {
        grossAmount: amount.toString(),
        netAmount: netAmountToUser.toString(),
        feeAmount: feeAmount.toString(),
      },
    }),
    sendWithdrawal(netAmountToUser, user.ton_wallet_address!),
  ]);

  // Update 24-hour withdrawal tracking after successful transfer
  await updateWithdrawalTracking({
    userId,
    withdrawnAmount: amount,
  });

  return transferResult;
}
