import { HttpsError, onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../constants";
import {
  getRevenueUser,
  validateRevenueBalance,
  validateSufficientBalance,
  processRevenueWithdrawal,
} from "./revenue-function.service";
import {
  logRevenueWithdrawal,
  logRevenueWithdrawalError,
} from "./revenue-function.logger";
import {
  validateWithdrawRevenueParams,
  validateAdminAccess,
  throwUnauthenticated,
  throwRevenueInternalError,
} from "./revenue-function.validator";

export const withdrawRevenue = onCall<{
  withdrawAmount: number;
  johnDowWallet: string;
}>(commonFunctionsConfig, async (request) => {
  if (!request.auth) {
    throwUnauthenticated();
  }

  const { withdrawAmount, johnDowWallet } = request.data;

  try {
    const userId = request.auth.uid;

    validateWithdrawRevenueParams({
      withdrawAmount,
      johnDowWallet,
    });

    await validateAdminAccess(userId);

    const revenueUser = await getRevenueUser();

    validateRevenueBalance(revenueUser, withdrawAmount);

    await validateSufficientBalance(withdrawAmount);

    const transferResult = await processRevenueWithdrawal(
      withdrawAmount,
      johnDowWallet
    );

    logRevenueWithdrawal({
      withdrawAmount,
      johnDowWallet,
      userId,
      transactionHash: transferResult.transactionHash,
    });

    return {
      success: transferResult.success,
      message: `Successfully transferred ${withdrawAmount.toFixed(
        4
      )} TON revenue to John Dow`,
      totalAmount: withdrawAmount,
      transactionHash: transferResult.transactionHash,
    };
  } catch (error) {
    logRevenueWithdrawalError({
      error,
      withdrawAmount,
      johnDowWallet,
      userId: request.auth?.uid,
    });

    if (error instanceof HttpsError) {
      throw error;
    }
    throwRevenueInternalError();
  }
});
