import { HttpsError } from "firebase-functions/v2/https";

export function throwInvalidParameters(): never {
  throw new HttpsError(
    "invalid-argument",
    "Order ID and resell price are required and must be valid"
  );
}

export function throwOrderNotFound(): never {
  throw new HttpsError("not-found", "Order not found");
}

export function throwNotOrderBuyer(): never {
  throw new HttpsError(
    "permission-denied",
    "You are not the buyer of this order"
  );
}

export function throwInvalidOrderStatus(): never {
  throw new HttpsError(
    "failed-precondition",
    "Order must have status 'gift_sent_to_relayer' to be resold"
  );
}

export function throwFulfillAndResellInternalError(message?: string): never {
  throw new HttpsError(
    "internal",
    message ?? "Server error while fulfilling and reselling order."
  );
}
