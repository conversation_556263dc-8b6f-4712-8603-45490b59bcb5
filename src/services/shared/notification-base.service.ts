import bot from "../../bot";
import { T } from "../../i18n";
import { createOrderDeepLink } from "../order-service/order.service";
import { NotificationServiceLogger } from "../notification-service/notification-service.logger";
import { getUserDataWithSession } from "../user-service/user.service";

export interface BaseNotificationParams {
  orderId: string;
  userId: string;
  orderNumber?: number;
}

export interface NotificationResult {
  success: boolean;
  message: string;
}

export interface MessageConfig {
  messageId: string;
  params: Record<string, any>;
}

export async function sendNotificationToUser(
  params: BaseNotificationParams,
  messageConfig: MessageConfig,
  loggerType: "seller" | "buyer"
): Promise<NotificationResult> {
  try {
    const { orderId, userId, orderNumber } = params;

    // Log notification start
    const logParams = {
      orderId,
      [loggerType === "seller" ? "sellerId" : "buyerId"]: userId,
      ...(orderNumber !== undefined && { orderNumber }),
      // Include additional params for logging (like price, proposedPrice, etc.)
      ...Object.fromEntries(
        Object.entries(messageConfig.params).filter(([key]) =>
          ["price", "proposedPrice", "originalPrice"].includes(key)
        )
      ),
    };

    if (loggerType === "seller") {
      NotificationServiceLogger.logSellerNotificationStart(logParams);
    } else {
      NotificationServiceLogger.logBuyerNotificationStart(logParams);
    }

    // Get user data and session
    const userData = await getUserDataWithSession(userId);
    const orderLink = createOrderDeepLink(orderId);

    // Create context for translation
    const ctx = {
      userLanguage: userData.language,
    };

    // Prepare message parameters with order link
    const messageParams = {
      orderNumber: orderNumber || "N/A",
      orderLink,
      ...messageConfig.params,
    };

    const message = T(ctx, messageConfig.messageId, messageParams);

    // Send notification
    await bot.telegram.sendMessage(userData.tgId, message, {
      parse_mode: "Markdown",
      link_preview_options: { is_disabled: true },
    });

    // Log success
    const successParams = {
      orderId,
      [loggerType === "seller" ? "sellerId" : "buyerId"]: userId,
      tgId: userData.tgId,
    };

    if (loggerType === "seller") {
      NotificationServiceLogger.logSellerNotificationSuccess(successParams);
    } else {
      NotificationServiceLogger.logBuyerNotificationSuccess(successParams);
    }

    return {
      success: true,
      message: `${loggerType} notification sent successfully`,
    };
  } catch (error) {
    // Log error
    const errorParams = {
      orderId: params.orderId,
      [loggerType === "seller" ? "sellerId" : "buyerId"]: params.userId,
    };

    if (loggerType === "seller") {
      NotificationServiceLogger.logSellerNotificationError(error, errorParams);
    } else {
      NotificationServiceLogger.logBuyerNotificationError(error, errorParams);
    }

    return {
      success: false,
      message: `Failed to send notification: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}
