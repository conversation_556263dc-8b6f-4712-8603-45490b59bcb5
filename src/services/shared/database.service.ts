import {
  DBGiftsCollection,
  DBOrdersCollection,
  DBUserCollection,
} from "../../firebase/firebase-admin";
import {
  GiftEntity,
  OrderEntity,
  UserEntity,
  GiftStatus,
} from "../../marketplace-shared";

export type EntityWithId<T> = T & { id: string };

export async function getEntityById<T>(
  collection: FirebaseFirestore.CollectionReference,
  id: string
): Promise<EntityWithId<T> | null> {
  const doc = await collection.doc(id).get();
  if (!doc.exists) {
    return null;
  }
  return { id: doc.id, ...doc.data() } as EntityWithId<T>;
}

export async function getGiftById(
  giftId: string
): Promise<EntityWithId<GiftEntity> | null> {
  return getEntityById<GiftEntity>(DBGiftsCollection, giftId);
}

export async function getOrderById(
  orderId: string
): Promise<EntityWithId<OrderEntity> | null> {
  return getEntityById<OrderEntity>(DBOrdersCollection, orderId);
}

export async function getUserById(
  userId: string
): Promise<EntityWithId<UserEntity> | null> {
  return getEntityById<UserEntity>(DBUserCollection, userId);
}

export async function getGiftsByOwnerAndStatus(
  ownerTgId: string,
  status: GiftStatus
): Promise<EntityWithId<GiftEntity>[]> {
  const query = await DBGiftsCollection.where("owner_tg_id", "==", ownerTgId)
    .where("status", "==", status)
    .get();

  return query.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  })) as EntityWithId<GiftEntity>[];
}

export async function getAllOrders(): Promise<EntityWithId<OrderEntity>[]> {
  const query = await DBOrdersCollection.get();
  return query.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  })) as EntityWithId<OrderEntity>[];
}

export async function getOrdersByGiftId(
  giftId: string
): Promise<EntityWithId<OrderEntity>[]> {
  const query = await DBOrdersCollection.where("giftId", "==", giftId).get();

  return query.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  })) as EntityWithId<OrderEntity>[];
}
