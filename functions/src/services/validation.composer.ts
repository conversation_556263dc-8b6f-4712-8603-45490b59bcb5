import {
  UserEntity,
  UserType,
  OrderEntity,
  CollectionEntity,
} from "../marketplace-shared";

// Type definitions for validation functions
// eslint-disable-next-line no-unused-vars
export type ValidationFunction<T> = (input: T) => void | Promise<void>;
// eslint-disable-next-line no-unused-vars
export type ValidationFunctionWithReturn<T, R> = (input: T) => R | Promise<R>;

// Validation result type for composable validations
export interface ValidationResult<T = any> {
  isValid: boolean;
  data?: T;
  error?: Error;
}

// Compose multiple validation functions that return void
export function composeValidations<T>(
  ...validations: ValidationFunction<T>[]
): ValidationFunction<T> {
  return async (input: T) => {
    for (const validation of validations) {
      await validation(input);
    }
  };
}

// Compose validation functions with data transformation
export function composeValidationsWithData<T, R>(
  ...validations: ValidationFunctionWithReturn<T, R>[]
): ValidationFunctionWithReturn<T, R[]> {
  return async (input: T) => {
    const results: R[] = [];
    for (const validation of validations) {
      const result = await validation(input);
      results.push(result);
    }
    return results;
  };
}

// Safe validation wrapper that catches errors
export function safeValidation<T>(
  validation: ValidationFunction<T>
): ValidationFunctionWithReturn<T, ValidationResult> {
  return async (input: T) => {
    try {
      await validation(input);
      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error : new Error(String(error)),
      };
    }
  };
}

// Conditional validation - only run if condition is met
export function conditionalValidation<T>(
  // eslint-disable-next-line no-unused-vars
  condition: (input: T) => boolean,
  validation: ValidationFunction<T>
): ValidationFunction<T> {
  return async (input: T) => {
    if (condition(input)) {
      await validation(input);
    }
  };
}

// Validation pipeline for complex scenarios
export class ValidationPipeline<T> {
  private readonly validations: ValidationFunction<T>[] = [];

  add(validation: ValidationFunction<T>): this {
    this.validations.push(validation);
    return this;
  }

  addConditional(
    // eslint-disable-next-line no-unused-vars
    condition: (input: T) => boolean,
    validation: ValidationFunction<T>
  ): this {
    this.validations.push(conditionalValidation(condition, validation));
    return this;
  }

  async execute(input: T): Promise<void> {
    for (const validation of this.validations) {
      await validation(input);
    }
  }

  async executeSafe(input: T): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];
    for (const validation of this.validations) {
      const result = await safeValidation(validation)(input);
      results.push(result);
    }
    return results;
  }
}

// Common validation parameter types
export interface OrderCreationValidationParams {
  userId: string;
  collectionId: string;
  price: number;
  userType: UserType;
}

export interface OrderPurchaseValidationParams {
  userId: string;
  orderId: string;
  userType: UserType;
}

export interface UserValidationParams {
  user: UserEntity;
  operation?: string;
}

export interface OrderValidationParams {
  order: OrderEntity;
  userId: string;
}

export interface CollectionValidationParams {
  collection: CollectionEntity;
  userType?: UserType;
  amount?: number;
}

// Validation chain builder for complex scenarios
export class ValidationChain<T> {
  private readonly steps: Array<{
    name: string;
    validation: ValidationFunction<T>;
    optional?: boolean;
  }> = [];

  addStep(
    name: string,
    validation: ValidationFunction<T>,
    optional = false
  ): this {
    this.steps.push({ name, validation, optional });
    return this;
  }

  async execute(input: T): Promise<void> {
    for (const step of this.steps) {
      try {
        await step.validation(input);
      } catch (error) {
        if (!step.optional) {
          throw error;
        }
        // Optional step failed, continue
      }
    }
  }

  async executeWithResults(
    input: T
  ): Promise<Array<{ name: string; success: boolean; error?: Error }>> {
    const results: Array<{ name: string; success: boolean; error?: Error }> =
      [];

    for (const step of this.steps) {
      try {
        await step.validation(input);
        results.push({ name: step.name, success: true });
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        results.push({ name: step.name, success: false, error: err });

        if (!step.optional) {
          break; // Stop on first required validation failure
        }
      }
    }

    return results;
  }
}

// Factory functions for common validation pipelines
export function createOrderCreationPipeline(): ValidationPipeline<OrderCreationValidationParams> {
  return new ValidationPipeline<OrderCreationValidationParams>();
}

export function createOrderPurchasePipeline(): ValidationPipeline<OrderPurchaseValidationParams> {
  return new ValidationPipeline<OrderPurchaseValidationParams>();
}

export function createUserValidationPipeline(): ValidationPipeline<UserValidationParams> {
  return new ValidationPipeline<UserValidationParams>();
}

// Factory functions for validation chains
export function createOrderCreationChain(): ValidationChain<OrderCreationValidationParams> {
  return new ValidationChain<OrderCreationValidationParams>();
}

export function createOrderPurchaseChain(): ValidationChain<OrderPurchaseValidationParams> {
  return new ValidationChain<OrderPurchaseValidationParams>();
}

// Parallel validation executor
export async function executeValidationsInParallel<T>(
  input: T,
  validations: ValidationFunction<T>[]
): Promise<ValidationResult[]> {
  const promises = validations.map((validation) =>
    safeValidation(validation)(input)
  );
  return Promise.all(promises);
}

// Sequential validation with early termination
export async function executeValidationsSequentially<T>(
  input: T,
  validations: ValidationFunction<T>[],
  stopOnFirstError = true
): Promise<ValidationResult[]> {
  const results: ValidationResult[] = [];

  for (const validation of validations) {
    const result = await safeValidation(validation)(input);
    results.push(result);

    if (!result.isValid && stopOnFirstError) {
      break;
    }
  }

  return results;
}
