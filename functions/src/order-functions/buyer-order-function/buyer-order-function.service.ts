import { UserType } from "../../marketplace-shared";
import { refundProposals } from "../../proposal-functions/proposal-service";
import { processPurchase } from "../../services/purchase-service/purchase-service";

export async function processBuyerPurchase(params: {
  buyerId: string;
  orderId: string;
}) {
  const { buyerId, orderId } = params;

  // Run proposal refund and purchase processing in parallel where possible
  // Note: refundAllActiveProposals must complete before processPurchase for data consistency
  await refundProposals(orderId);

  const result = await processPurchase({
    userId: buyerId,
    orderId,
    userType: UserType.BUYER,
  });

  return result;
}
