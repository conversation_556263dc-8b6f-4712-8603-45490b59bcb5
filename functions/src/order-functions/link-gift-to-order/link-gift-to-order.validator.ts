import { CallableRequest, HttpsError } from "firebase-functions/v2/https";
import { GIFT_ERRORS, ORDER_ERRORS } from "../../error-messages";
import { LinkGiftToOrderLogger } from "./link-gift-to-order.logger";

export function throwOrderNotFound(): never {
  throw new HttpsError(
    "not-found",
    JSON.stringify({
      errorKey: ORDER_ERRORS.ORDER_NOT_FOUND,
      fallbackMessage: "Order not found",
    })
  );
}

export function throwGiftNotOwnedByUser(): never {
  throw new HttpsError(
    "permission-denied",
    JSON.stringify({
      errorKey: GIFT_ERRORS.GIFT_NOT_OWNED_BY_USER,
      fallbackMessage: "Gift does not belong to the current user",
    })
  );
}

export function throwOrderNotOwnedByUser(): never {
  throw new HttpsError(
    "permission-denied",
    JSON.stringify({
      errorKey: ORDER_ERRORS.ORDER_NOT_FOUND,
      fallbackMessage: "User is not the seller of this order",
    })
  );
}

export function throwInvalidOrderStatus(status: string): never {
  throw new HttpsError(
    "failed-precondition",
    JSON.stringify({
      errorKey: ORDER_ERRORS.INVALID_ORDER_STATUS,
      fallbackMessage: `Order status must be 'created' or 'paid', but was '${status}'`,
    })
  );
}

export function throwGiftCollectionMismatch(): never {
  throw new HttpsError(
    "failed-precondition",
    JSON.stringify({
      errorKey: GIFT_ERRORS.GIFT_COLLECTION_MISMATCH,
      fallbackMessage: "Gift and order must be from the same collection",
    })
  );
}

export function handleLinkGiftToOrderError(error: unknown) {
  LinkGiftToOrderLogger.logLinkGiftToOrderError(error);

  if (error instanceof HttpsError) {
    return {
      success: false,
      message: error.message,
    };
  }

  return {
    success: false,
    message: "An unexpected error occurred while linking gift to order",
  };
}

interface LinkGiftToOrderRequest {
  giftId: string;
  orderId: string;
}

export const validateLinkGiftToOrderRequestParams = (
  request: CallableRequest
) => {
  if (!request.data) {
    throw new HttpsError("invalid-argument", "Request data is required");
  }

  if (!request.auth?.uid) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  const { giftId, orderId } = request.data as LinkGiftToOrderRequest;

  if (!giftId || !orderId) {
    throw new HttpsError("invalid-argument", "giftId and orderId are required");
  }

  return { giftId, orderId };
};
