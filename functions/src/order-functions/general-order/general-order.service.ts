import { DBOrdersCollection } from "../../services/db.service";
import { OrderEntity } from "../../marketplace-shared";
import { processOrderCancellation } from "../../services/order-service/order-cancellation-service";
import {
  throwCancellationFailed,
  throwOrderNotFound,
} from "./general-order.validator";
import { validateCancellationPermission } from "../../services/order-service/order-validation-service";

export async function cancelOrder({
  orderId,
  userId,
}: {
  orderId: string;
  userId: string;
}) {
  const orderDoc = await DBOrdersCollection.doc(orderId).get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  try {
    await validateCancellationPermission(order, userId);

    const result = await processOrderCancellation(order, userId);

    return {
      success: result.success,
      message: result.message,
      order: {
        id: order.id!,
        number: order.number,
        status: "cancelled",
      },
      feeApplied: result?.feeApplied ?? 0,
      feeType: result.feeType,
    };
  } catch (error) {
    if (error instanceof Error) {
      throwCancellationFailed(error.message);
    }
    throw error;
  }
}
