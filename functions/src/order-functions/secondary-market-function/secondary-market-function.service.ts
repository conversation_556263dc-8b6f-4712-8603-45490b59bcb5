import * as admin from "firebase-admin";
import {
  OrderEntity,
  OrderStatus,
  ResellTxHistoryEntity,
} from "../../marketplace-shared";
import { refundProposals } from "../../proposal-functions/proposal-service";
import { updateUserBalance } from "../../services/balance-service/balance-service";
import { validateSufficientBalance } from "../../services/financial.validator";
import { DBOrdersCollection } from "../../services/db.service";
import {
  applyResellPurchaseFee,
  calculateFeeAmount,
  getAppConfig,
} from "../../services/fee-service/fee-service";
import { bpsToDecimal, safeMultiply, safeSubtract } from "../../utils";
import {
  throwInvalidOrderStatus,
  throwMissingParticipants,
  throwNotOrderBuyer,
  throwOrderNotFound,
  throwPriceBelowMinimum,
  throwPriceExceedsCollateral,
} from "./secondary-market-function.validator";
import {
  validateOrderExists,
  validateSecondaryMarketAvailability,
  validateSecondaryMarketOrderStatus,
  validateSecondaryMarketPurchasePermissions,
  validateOrderParticipants,
} from "../../services/order-service/order-validation-service";
import { validateUserExists } from "../../services/auth-service/auth.validator";

export interface SetSecondaryPriceParams {
  orderId: string;
  secondaryMarketPrice: number;
  userId: string;
}

export interface SetSecondaryPriceResult {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
}

export interface SecondaryPurchaseParams {
  orderId: string;
  newBuyerId: string;
}

export interface SecondaryPurchaseResult {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
  newBuyerId: string;
  oldBuyerId: string;
  netAmountToOldBuyer: number;
  feeAmount: number;
  lockedAmount: number;
}

export async function setSecondaryMarketPrice({
  orderId,
  secondaryMarketPrice,
  userId,
}: SetSecondaryPriceParams): Promise<SetSecondaryPriceResult> {
  // Parallelize order fetching and config loading
  const [orderDoc, config] = await Promise.all([
    DBOrdersCollection.doc(orderId).get(),
    getAppConfig(),
  ]);

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  if (order.status !== OrderStatus.PAID) {
    throwInvalidOrderStatus();
  }

  if (order.buyerId !== userId) {
    throwNotOrderBuyer();
  }

  if (!order.buyerId || !order.sellerId) {
    throwMissingParticipants();
  }
  const minSecondaryMarketPrice = config?.min_secondary_market_price ?? 1;

  if (secondaryMarketPrice < minSecondaryMarketPrice) {
    throwPriceBelowMinimum(minSecondaryMarketPrice);
  }

  // Calculate total collateral (buyer + seller locked amounts)
  const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage ?? 0;
  const sellerLockPercentageBPS = order.fees?.seller_locked_percentage ?? 0;
  const buyerLockPercentage = bpsToDecimal(buyerLockPercentageBPS);
  const sellerLockPercentage = bpsToDecimal(sellerLockPercentageBPS);

  const buyerLockedAmount = safeMultiply(order.price, buyerLockPercentage);
  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);
  const totalCollateral = buyerLockedAmount + sellerLockedAmount;

  if (secondaryMarketPrice > totalCollateral) {
    throwPriceExceedsCollateral(
      totalCollateral,
      buyerLockedAmount,
      sellerLockedAmount
    );
  }

  await DBOrdersCollection.doc(orderId).update({
    secondaryMarketPrice,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });

  return {
    success: true,
    message: `Secondary market price set to ${secondaryMarketPrice} TON.`,
    orderId,
    secondaryMarketPrice,
  };
}

export async function makeSecondaryMarketPurchase({
  orderId,
  newBuyerId,
}: SecondaryPurchaseParams): Promise<SecondaryPurchaseResult> {
  // Parallelize proposal refunding and order/buyer fetching
  const [, order] = await Promise.all([
    refundProposals(orderId),
    validateOrderExists(orderId),
    validateUserExists(newBuyerId),
  ]);

  // Use centralized validation functions
  validateSecondaryMarketAvailability(order);
  validateSecondaryMarketOrderStatus(order);
  validateSecondaryMarketPurchasePermissions(order, newBuyerId);
  validateOrderParticipants(order);

  // Use fees from order object instead of app_config
  const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage || 0;
  const buyerLockPercentage = bpsToDecimal(buyerLockPercentageBPS);

  // After validation, we know secondaryMarketPrice exists and is > 0
  const secondaryMarketPrice = order.secondaryMarketPrice!;
  // After validateOrderParticipants, we know buyerId exists
  const oldBuyerId = order.buyerId!;

  // Calculate old buyer's locked amount (from original order)
  const oldBuyerLockedAmount = safeMultiply(order.price, buyerLockPercentage);

  // Apply resell purchase fee (no referral fees for reselling)
  const resellPurchaseFeeBPS = order.fees?.resell_purchase_fee || 0;

  const feeResult = await applyResellPurchaseFee({
    buyerId: newBuyerId,
    amount: secondaryMarketPrice,
    resellPurchaseFeeBPS,
  });

  const netAmountToOldBuyer = safeSubtract(
    secondaryMarketPrice,
    feeResult.totalFee
  );

  // Validate new buyer has sufficient funds for secondary market purchase
  await validateSufficientBalance(
    newBuyerId,
    secondaryMarketPrice,
    "secondary market purchase"
  );

  // Step 1: New buyer pays secondary market price and gets locked amount from old buyer
  await updateUserBalance({
    userId: newBuyerId,
    sumChange: -secondaryMarketPrice + oldBuyerLockedAmount,
    lockedChange: oldBuyerLockedAmount,
  });

  // Step 2: Old buyer receives net amount and loses their locked amount
  await updateUserBalance({
    userId: oldBuyerId,
    sumChange: netAmountToOldBuyer - oldBuyerLockedAmount,
    lockedChange: -oldBuyerLockedAmount,
  });

  // Step 3: Calculate and accumulate reseller earnings for seller
  const resellPurchaseFeeForSellerBPS =
    order.fees?.resell_purchase_fee_for_seller || 0;
  const resellerEarningsForSeller =
    resellPurchaseFeeForSellerBPS > 0
      ? calculateFeeAmount(secondaryMarketPrice, resellPurchaseFeeForSellerBPS)
      : 0;

  const currentResellerEarnings = order.reseller_earnings_for_seller ?? 0;
  const newResellerEarnings =
    currentResellerEarnings + resellerEarningsForSeller;

  // Update order with new buyer, clear secondary market price, and update reseller earnings
  await DBOrdersCollection.doc(orderId).update({
    buyerId: newBuyerId,
    secondaryMarketPrice: null,
    reseller_earnings_for_seller: newResellerEarnings,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });

  // Create resell transaction history record as sub-collection under the order
  const resellTxHistory: ResellTxHistoryEntity = {
    order_id: orderId,
    execution_price: secondaryMarketPrice.toString(),
    reseller_id: oldBuyerId,
    executed_at: admin.firestore.FieldValue.serverTimestamp() as any,
    buyer_id: newBuyerId,
  };

  await DBOrdersCollection.doc(orderId)
    .collection("resell_tx_history")
    .add(resellTxHistory);

  return {
    success: true,
    message: `Secondary market purchase successful! Paid ${secondaryMarketPrice} TON (${feeResult.totalFee} TON fee). ${oldBuyerLockedAmount} TON locked.`,
    orderId,
    secondaryMarketPrice,
    newBuyerId,
    oldBuyerId,
    netAmountToOldBuyer,
    feeAmount: feeResult.totalFee,
    lockedAmount: oldBuyerLockedAmount,
  };
}
