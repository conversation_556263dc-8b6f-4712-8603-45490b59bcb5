import * as admin from "firebase-admin";
import { TxLookupEntity } from "../marketplace-shared";

import { DBTxLookupDoc } from "../services/db.service";
import {
  throwTxLookupGetError,
  throwTxLookupUpdateError,
} from "./tx-lookup-function.validator";
import { TxLookupLogger } from "./tx-lookup-function.logger";

export async function getTxLookup() {
  try {
    const doc = await DBTxLookupDoc.get();

    if (!doc.exists) {
      return null;
    }

    return {
      id: doc.id,
      ...doc.data(),
    } as TxLookupEntity;
  } catch (error) {
    TxLookupLogger.logTxLookupGetError({ error });
    throwTxLookupGetError((error as any).message);
  }
}

export async function updateTxLookup(
  lastCheckedRecordId: string
): Promise<void> {
  try {
    await DBTxLookupDoc.set(
      {
        last_checked_record_id: lastCheckedRecordId,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      },
      { merge: true }
    );

    TxLookupLogger.logTxLookupUpdated({ lastCheckedRecordId });
  } catch (error) {
    TxLookupLogger.logTxLookupUpdateError({ error, lastCheckedRecordId });
    throwTxLookupUpdateError((error as any).message);
  }
}
