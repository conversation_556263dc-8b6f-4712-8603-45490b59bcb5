{"businessConnection.giftReadyForBuyer": "🎁 Отличные новости! Ваш подарок для заказа #{orderNumber} готов к доставке. Пожалуйста, проверьте ваши заказы.", "businessConnection.giftSentError": "❌ Не удалось отправить подарок релейеру. Пожалуйста, попробуйте еще раз.", "businessConnection.giftSentSuccess": "✅ Подарок успешно отправлен релейеру! Покупатель будет уведомлен.", "businessConnection.giftTransferGenericError": "❌ Не удалось передать подарок. Пожалуйста, попробуйте еще раз.", "businessConnection.giftTransferredSuccess": "✅ Подарок успешно передан!", "businessConnection.incorrectGift": "❌ Этот подарок не соответствует требованиям заказа. Пожалуйста, отправьте правильный подарок.", "businessConnection.noGiftToTransfer": "❌ Подарок для передачи не найден.", "businessConnection.orderNotFound": "❌ Заказ не найден. Пожалуйста, проверьте ID заказа и попробуйте еще раз.", "businessConnection.processingGift": "⏳ Обрабатываем ваш подарок...", "businessConnection.processingWithdrawal": "⏳ Обрабатываем ваш вывод...", "businessConnection.withdrawalError": "❌ Не удалось вывести подарок. Пожалуйста, попробуйте еще раз.", "businessConnection.withdrawalSuccess": "✅ Подарок успешно выведен!", "buttons.backToOrders": "🔙 Назад к заказам", "buttons.buyOrders": "🛒 Заказы на покупку", "buttons.cancel": "❌ Отмена", "buttons.contactSupport": "📞 Связаться с поддержкой", "buttons.depositGift": "📦 Внести подарок", "buttons.myGifts": "🎁 Мои подарки", "buttons.openMarketplace": "🌐 Открыть маркетплейс", "buttons.openMarketplaceButton": "🌐 Открыть маркетплейс", "buttons.orderHelpButton": "📋 Помощь по заказу", "buttons.readyToSendGift": "🎁 Я готов отправить подарок", "buttons.sellOrders": "💰 Заказы на продажу", "buttons.viewAllOrders": "📋 Посмотреть все заказы", "buttons.viewMyOrders": "👤 Посмотреть мои заказы", "buttons.withdrawGift": "Вывести {giftName} {orderInfo}", "callbacks.backToMenu": "🏠 Назад в главное меню", "callbacks.buyOrdersTitle": "🛒 Ваши заказы на покупку (всего {count})", "commands.health.description": "Проверить состояние бота", "commands.health.error": "❌ Ошибка при проверке состояния", "commands.health.lastCheck": "{status}\n\nПоследняя проверка: {timestamp}", "commands.health.noData": "❌ Данные проверки состояния не найдены", "commands.health.statusHealthy": "✅ Работает", "commands.health.statusUnhealthy": "⚠️ Не работает", "commands.help.description": "Показать справочную информацию", "commands.start.description": "Запустить бота и показать главное меню", "common.botDescription": "Добро пожаловать в {APP_NAME}🎁 – первый ликвидный предрынок для неулучшенных подарков Telegram!\nС {APP_NAME} вы можете:\n\nКак покупатель:\n\n🔓 Купить любой неулучшенный подарок TG.\n💸 Перепродать для мгновенной прибыли\n\nКак продавец:\n\n🎁 Продать неулучшенный подарок TG всего с 50% залогом.\n💰 Зарабатывать комиссии с перепродаж\n\nНаслаждайтесь быстрой, безопасной и простой торговлей подарками!", "common.botGenericError": "Извините, что-то пошло не так. Пожалуйста, попробуйте позже.", "common.botShortDescription": "🎁 {APP_NAME} - Маркетплейс подарков Telegram", "common.genericError": "❌ Не удалось обработать ваш запрос. Пожалуйста, попробуйте позже.", "common.giftSelectedForWithdrawal": "Подарок выбран для вывода", "common.giftWithdrawalInstructions": "✅ Подарок выбран для вывода! Теперь перейдите к @{relayerUsername} и отправьте команду 'get a gift', чтобы завершить вывод.", "common.giftWithdrawalSimulatedSuccessfully": "Вывод подарка успешно симулирован", "common.giftWithdrawalSimulationFailed": "❌ Режим симуляции: Не удалось вывести подарок", "common.giftWithdrawalSimulationFailedCallback": "Симуляция вывода подарка не удалась", "common.giftWithdrawalSimulationSuccess": "🎉 Режим симуляции: Подарок успешно выведен!", "common.help": "Добро пожаловать в {APP_NAME}! \n \n@prem_channel - Сообщество \n@prem_support_official - Поддержка \n{relayerUsername} - Релейер подарков", "common.telegramIdError": "❌ Не удается определить ваш Telegram ID. Пожалуйста, попробуйте еще раз.", "common.welcome": "🛍️ Добро пожаловать в {APP_NAME} Bot!", "common.withdrawalErrorFallback": "Не удалось вывести подарок", "gifts.depositSuccess": "🎁 Подарок успешно внесен! Теперь вы можете увидеть свой подарок в приложении {APP_NAME} во вкладке 'Мои подарки'.", "gifts.fetchError": "❌ Ошибка при загрузке ваших подарков. Пожалуйста, попробуйте позже.", "gifts.fetchingGifts": "🔄 Загружаем ваши подарки...", "gifts.giftsAvailableForWithdrawal": "🎁 Ваши подарки доступные для вывода ({count})", "gifts.linkedOrder": "📦 Заказ #{orderNumber} ({orderStatus})", "gifts.noGiftsAvailable": "📭 Подарки недоступны. Отправьте подарки {relayerUsername} чтобы начать!", "gifts.noLinkedOrder": "📦 Нет связанного заказа", "gifts.unknownGift": "Неизвестный подарок", "gifts.withdrawInstructions": "Используйте кнопки ниже, чтобы вывести конкретные подарки или управлять своими заказами.", "language.detectionPromptRussian": "🌍 Мы обнаружили, что вы используете приложение на русском языке. Хотите переключиться на русский?", "language.detectionPromptUkrainian": "🌍 Мы обнаружили, что вы используете приложение на украинском языке. Хотите переключиться на украинский?", "language.keepEnglish": "Нет, я хочу остаться на английском", "language.setToEnglish": "✅ Язык установлен на английский. Добро пожаловать в {APP_NAME} Bot!", "language.setToRussian": "✅ Язык изменен на русский. Добро пожаловать в {APP_NAME} Bot!", "language.setToUkrainian": "✅ Мову змінено на українську. Ласкаво просимо до {APP_NAME} Bot!", "language.switchToRussian": "Да, давайте переключимся на русский", "language.switchToUkrainian": "Да, давайте переключимся на украинский", "notifications.buyerGiftSent": "🎁 Отличные новости! Подарок для вашего заказа #{orderNumber} был отправлен боту. Теперь вы можете получить его у @{relayerUsername}.", "notifications.buyerGiftSentWithLink": "🎁 Отличные новости! Подарок для вашего заказа #{orderNumber} был отправлен боту. Теперь вы можете получить его у @{relayerUsername}.\n\n📱 Посмотреть детали заказа: {orderLink}", "notifications.newProposalReceived": "💰 Получено новое предложение цены для вашего заказа #{orderNumber}! Кто-то предложил {proposedPrice} TON (первоначальная цена: {originalPrice} TON).\n\n📱 Посмотреть детали заказа: {orderLink}", "notifications.proposalAccepted": "✅ Отличные новости! Ваше предложение цены {proposedPrice} TON для заказа #{orderNumber} принято продавцом.\n\n📱 Посмотреть детали заказа: {orderLink}", "notifications.sellerOrderPaid": "🎉 Поздравляем! Ваш заказ #{orderNumber} был куплен за {price} TON. Теперь вы можете отправить подарок для завершения заказа.", "notifications.sellerOrderPaidWithLink": "🎉 Поздравляем! Ваш заказ #{orderNumber} был куплен за {price} TON. Теперь вы можете отправить подарок для завершения заказа.\n\n📱 Посмотреть детали заказа: {orderLink}", "simulation.giftDepositError": "❌ Не удалось внести тестовый подарок: {errorMessage}\n\nПожалуйста, попробуйте еще раз или обратитесь в поддержку, если проблема не исчезнет.", "simulation.giftDepositMode": "🎁 Внести подарок (Режим симуляции)\n\n🔧 РЕЖИМ СИМУЛЯЦИИ: Генерируем и вносим тестовый подарок...", "simulation.giftDepositSuccess": "🎁 Детали подарка:\n• Название: {giftName}\n• Модель: {modelName}\n• Символ: {symbolName}\n• Фон: {backdropName}\n\nВаш подарок теперь доступен в приложении {APP_NAME} во вкладке 'Мои подарки'.", "simulation.giftWithdrawalError": "❌ Вывод подарка не удался в режиме симуляции: {errorMessage}\n\nПожалуйста, попробуйте еще раз или обратитесь в поддержку, если проблема не исчезнет.", "simulation.giftWithdrawalMode": "🎁 Вывод подарка (Режим симуляции)\n\n🔧 РЕЖИМ СИМУЛЯЦИИ: Обрабатываем вывод подарка...", "simulation.giftWithdrawalSuccess": "✅ Вывод подарка успешно завершен в режиме симуляции!\n\n🔧 РЕЖИМ СИМУЛЯЦИИ: Передача подарка пропущена. В реальном режиме ваш подарок был бы передан вам сейчас.", "simulation.orderActivation": "🔧 РЕЖИМ РАЗРАБОТКИ: Это симуляция. В продакшене вам нужно сначала внести подарок.", "simulation.orderView": "🔧 РЕЖИМ РАЗРАБОТКИ: Этот заказ в режиме симуляции.", "simulation.sellerGiftDeposit": "🔧 РЕЖИМ РАЗРАБОТКИ: Это симуляция. В продакшене вы бы отправили подарок @premrelayer.", "support.contactInfo": "📞 Для поддержки, пожалуйста, обратитесь к @prem_support_official"}