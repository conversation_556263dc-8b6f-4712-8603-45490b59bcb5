import { LogOperations } from "../../constants";
import { TRANSACTION_DESCRIPTION_INTL_KEYS } from "../../transaction-description-intl-keys";
import {
  MARKETPLACE_REVENUE_USER_ID,
  Role,
  TxType,
  UserEntity,
} from "../../marketplace-shared";
import { bpsToDecimal, safeMultiply, safeSubtract } from "../../utils";
import {
  addFunds,
  addFundsWithHistory,
  updateUserBalance,
} from "../balance-service/balance-service";
import { validateSufficientBalance } from "../financial.validator";
import { DBUserCollection } from "../db.service";
import { getCachedAppConfig } from "../performance-cache-service";
import { createTransactionRecord } from "../transaction-history-service/transaction-history-service";
import { FeeServiceLogger } from "./fee-service.logger";

// Types for fee processing
interface FeeResult {
  totalFee: number;
  referralFee: number;
  marketplaceFee: number;
}

interface StaticFeeParams {
  userId?: string;
  amount: number;
  feeConfigKey: keyof ReturnType<typeof getDefaultConfig>;
  feeType: string;
  operation: LogOperations;
}

// Default configuration factory
function getDefaultConfig() {
  return {
    deposit_fee: 0,
    withdrawal_fee: 0,
    withdrawal_gift_fee: 0,
    referrer_fee: 0,
    cancel_order_fee: 0,
    purchase_fee: 0,
    buyer_lock_percentage: 0,
    seller_lock_percentage: 0,
    resell_purchase_fee: 0,
    resell_purchase_fee_for_seller: 0,
    min_deposit_amount: 0,
    min_withdrawal_amount: 0,
    max_withdrawal_amount: 0,
    min_secondary_market_price: 0,
    fixed_cancel_order_fee: 0,
    cancel_price_proposal_fee: 0,
    lock_period: 21, // Default 21 days lock period
  };
}

export async function getAppConfig() {
  try {
    return await getCachedAppConfig();
  } catch (error) {
    FeeServiceLogger.logAppConfigNotFound();
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.APP_CONFIG_FETCH,
    });
    return getDefaultConfig();
  }
}

// Helper functions for common fee processing patterns
async function processStaticFee(params: StaticFeeParams): Promise<number> {
  const { userId, amount, feeConfigKey, feeType, operation } = params;

  try {
    const config = await getAppConfig();
    const feeAmount = config?.[feeConfigKey] || 0;

    if (!feeAmount || feeAmount <= 0 || feeAmount >= amount) {
      return userId ? 0 : amount; // Return 0 for fee, original amount for net calculation
    }

    if (userId) {
      await applyFeeToMarketplaceRevenue({ feeAmount, feeType });
      FeeServiceLogger.logFeeApplied({ feeAmount, feeType, userId });
      return feeAmount;
    }

    // For deposit fee, return net amount
    const netAmount = safeSubtract(amount, feeAmount);
    await applyFeeToMarketplaceRevenue({ feeAmount, feeType });
    FeeServiceLogger.logFeeApplied({ feeAmount, feeType, netAmount });
    return netAmount;
  } catch (error) {
    FeeServiceLogger.logFeeServiceError({
      error,
      operation,
      userId,
      amount,
    });
    throw error;
  }
}

async function processBPSFee(
  amount: number,
  feeBPS: number,
  userId: string,
  feeType: string
): Promise<FeeResult> {
  if (!feeBPS || feeBPS <= 0) {
    return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
  }

  const totalFeeAmount = calculateFeeAmount(amount, feeBPS);
  if (totalFeeAmount <= 0) {
    return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
  }

  await validateSufficientBalance(userId, totalFeeAmount, feeType);
  await updateUserBalance({
    userId,
    sumChange: -totalFeeAmount,
    lockedChange: 0,
  });

  return {
    totalFee: totalFeeAmount,
    referralFee: 0,
    marketplaceFee: totalFeeAmount,
  };
}

export function calculateFeeAmount(amount: number, feeBps: number) {
  if (!feeBps || feeBps <= 0) {
    return 0;
  }
  // BPS = basis points (1 BPS = 0.01%)
  return safeMultiply(amount, bpsToDecimal(feeBps));
}

export async function getAdminUser() {
  try {
    const adminQuery = await DBUserCollection.where("role", "==", Role.ADMIN)
      .limit(1)
      .get();

    if (adminQuery.empty) {
      FeeServiceLogger.logAdminUserNotFound();
      return null;
    }

    const adminDoc = adminQuery.docs[0];
    return {
      // @ts-expect-error note
      id: adminDoc.id,
      ...adminDoc.data(),
    } as UserEntity;
  } catch (error) {
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.ADMIN_USER_LOOKUP,
    });
    throw error;
  }
}

export async function applyFeeToMarketplaceRevenue(params: {
  feeAmount: number;
  feeType: string;
}) {
  const { feeAmount, feeType } = params;
  if (feeAmount <= 0) {
    return;
  }

  try {
    await addFunds(MARKETPLACE_REVENUE_USER_ID, feeAmount);
    FeeServiceLogger.logFeeApplied({
      feeAmount,
      feeType,
      userId: MARKETPLACE_REVENUE_USER_ID,
    });
  } catch (error) {
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.MARKETPLACE_REVENUE,
      amount: feeAmount,
    });
    throw error;
  }
}

export async function applyDepositFee(params: { depositAmount: number }) {
  const { depositAmount } = params;
  return processStaticFee({
    amount: depositAmount,
    feeConfigKey: "deposit_fee",
    feeType: "deposit",
    operation: LogOperations.DEPOSIT_FEE,
  });
}

export async function applyFixedCancelOrderFee(userId: string) {
  try {
    const config = await getAppConfig();
    if (!config?.fixed_cancel_order_fee) {
      return 0;
    }

    // Fixed cancel order fee is a static TON value, not BPS
    const feeAmount = config.fixed_cancel_order_fee;
    if (feeAmount <= 0) {
      return 0;
    }

    await validateSufficientBalance(
      userId,
      feeAmount,
      "fixed cancel order fee"
    );

    await updateUserBalance({ userId, sumChange: -feeAmount, lockedChange: 0 });

    // Record transaction history for penalty payment
    await createTransactionRecord({
      userId,
      txType: TxType.CANCELATION_FEE,
      amount: feeAmount,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.FIXED_CANCELLATION_FEE_PENALTY,
      descriptionIntlParams: {
        amount: feeAmount.toString(),
      },
      isReceivingCompensation: false,
    });

    await applyFeeToMarketplaceRevenue({
      feeAmount,
      feeType: "fixed_cancel_order",
    });

    FeeServiceLogger.logFeeApplied({
      feeAmount,
      feeType: "fixed_cancel_order",
      userId,
    });

    return feeAmount;
  } catch (error) {
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.FIXED_CANCEL_ORDER_FEE,
      userId,
    });
    throw error;
  }
}

export async function applyResellPurchaseFee(params: {
  buyerId: string;
  amount: number;
  resellPurchaseFeeBPS: number;
}) {
  const { buyerId, amount, resellPurchaseFeeBPS } = params;

  try {
    const result = await processBPSFee(
      amount,
      resellPurchaseFeeBPS,
      buyerId,
      "resell purchase fee"
    );

    if (result.totalFee > 0) {
      await applyFeeToMarketplaceRevenue({
        feeAmount: result.marketplaceFee,
        feeType: "resell_purchase",
      });

      FeeServiceLogger.logTotalFeeApplied({
        feeAmount: result.totalFee,
        feeType: "resell_purchase_total",
        referralFee: result.referralFee,
        marketplaceFee: result.marketplaceFee,
      });
    }

    return result;
  } catch (error) {
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.RESELL_PURCHASE_FEE,
    });
    throw error;
  }
}

// Helper function to get referrer fee rate
async function getReferrerFeeRate(
  referralId: string,
  defaultReferrerFeeBPS: number
): Promise<{ referrerId: string; referrerFeeRate: number } | null> {
  const referrerQuery = await DBUserCollection.where("id", "==", referralId)
    .limit(1)
    .get();

  if (referrerQuery.empty) {
    FeeServiceLogger.logReferrerNotFound({ referralId });
    return null;
  }

  const referrerDoc = referrerQuery.docs[0];
  const referrerId = referrerDoc.id;
  const referrerData = referrerDoc.data();

  let referrerFeeRate = 0;
  if (
    referrerData.referral_fee !== undefined &&
    referrerData.referral_fee > 0
  ) {
    referrerFeeRate = referrerData.referral_fee;
    FeeServiceLogger.logCustomReferralFee({ referrerFeeRate, referrerId });
  } else {
    referrerFeeRate = defaultReferrerFeeBPS;
    FeeServiceLogger.logOrderReferralFee({ referrerFeeRate, referrerId });
  }

  return { referrerId, referrerFeeRate };
}

// Helper function to process referral fee
async function processReferralFee(
  amount: number,
  referrerId: string,
  referrerFeeRate: number,
  referralId: string
): Promise<number> {
  if (referrerFeeRate <= 0) return 0;

  const referralFeeAmount = calculateFeeAmount(amount, referrerFeeRate);

  await addFundsWithHistory({
    userId: referrerId,
    amount: referralFeeAmount,
    txType: TxType.REFERRAL_FEE,
    descriptionIntlKey:
      TRANSACTION_DESCRIPTION_INTL_KEYS.REFERRAL_FEE_FROM_PURCHASE,
    descriptionIntlParams: { amount: referralFeeAmount.toString() },
  });

  FeeServiceLogger.logReferralFeeApplied({
    feeAmount: referralFeeAmount,
    feeType: "purchase_referral",
    referrerFeeRate,
    referrerId,
    referralId,
  });

  return referralFeeAmount;
}

export async function applyPurchaseFeeWithReferralFromOrder(params: {
  buyerId: string;
  amount: number;
  referralId?: string;
  purchaseFeeBPS: number;
  referrerFeeBPS: number;
}) {
  const { buyerId, amount, referralId, purchaseFeeBPS, referrerFeeBPS } =
    params;

  try {
    const baseResult = await processBPSFee(
      amount,
      purchaseFeeBPS,
      buyerId,
      "purchase fee with referral from order"
    );

    if (baseResult.totalFee === 0) {
      return baseResult;
    }

    let referralFeeAmount = 0;
    let marketplaceFeeAmount = baseResult.totalFee;

    if (referralId && referrerFeeBPS > 0) {
      const referrerInfo = await getReferrerFeeRate(referralId, referrerFeeBPS);

      if (referrerInfo) {
        referralFeeAmount = await processReferralFee(
          amount,
          referrerInfo.referrerId,
          referrerInfo.referrerFeeRate,
          referralId
        );
        marketplaceFeeAmount = safeSubtract(
          baseResult.totalFee,
          referralFeeAmount
        );
      }
    }

    if (marketplaceFeeAmount > 0) {
      await applyFeeToMarketplaceRevenue({
        feeAmount: marketplaceFeeAmount,
        feeType: "purchase",
      });
    }

    const result = {
      totalFee: baseResult.totalFee,
      referralFee: referralFeeAmount,
      marketplaceFee: marketplaceFeeAmount,
    };

    FeeServiceLogger.logTotalFeeApplied({
      feeAmount: result.totalFee,
      feeType: "purchase_total",
      referralFee: result.referralFee,
      marketplaceFee: result.marketplaceFee,
    });

    return result;
  } catch (error) {
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.PURCHASE_FEE_WITH_REFERRAL,
    });
    throw error;
  }
}

export async function applyWithdrawFee(userId: string, withdrawAmount: number) {
  return processStaticFee({
    userId,
    amount: withdrawAmount,
    feeConfigKey: "withdrawal_fee",
    feeType: "withdrawal",
    operation: LogOperations.WITHDRAWAL_FEE,
  });
}
