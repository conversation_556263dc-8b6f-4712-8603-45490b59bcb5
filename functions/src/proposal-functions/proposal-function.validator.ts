import { HttpsError } from "firebase-functions/v2/https";
import { PROPOSAL_ERRORS } from "../error-messages";

export function throwOrderNotFound(): never {
  throw new HttpsError(
    "not-found",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.ORDER_NOT_FOUND,
      fallbackMessage: "Order not found",
    })
  );
}

export function throwProposalOnlyOnSellOrders(): never {
  throw new HttpsError(
    "failed-precondition",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.PROPOSAL_ONLY_ON_SELL_ORDERS,
      fallbackMessage: "Proposals can only be made on sell orders",
    })
  );
}



export function throwCannotProposeOnSecondaryMarket(): never {
  throw new HttpsError(
    "failed-precondition",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.CANNOT_PROPOSE_ON_SECONDARY_MARKET,
      fallbackMessage: "Cannot propose on orders with secondary market price",
    })
  );
}

export function throwInvalidProposedPrice(): never {
  throw new HttpsError(
    "invalid-argument",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.INVALID_PROPOSED_PRICE,
      fallbackMessage: "Proposed price must be positive and less than current order price",
    })
  );
}

export function throwUserAlreadyHasActiveProposal(): never {
  throw new HttpsError(
    "already-exists",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.USER_ALREADY_HAS_ACTIVE_PROPOSAL,
      fallbackMessage: "User already has an active proposal for this order",
    })
  );
}

export function throwInsufficientBalance(): never {
  throw new HttpsError(
    "failed-precondition",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.INSUFFICIENT_BALANCE,
      fallbackMessage: "Insufficient balance for proposal collateral",
    })
  );
}

export function throwNoActiveProposalFound(): never {
  throw new HttpsError(
    "not-found",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.NO_ACTIVE_PROPOSAL_FOUND,
      fallbackMessage: "No active proposal found for this user",
    })
  );
}

export function throwProposalNotFound(): never {
  throw new HttpsError(
    "not-found",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.PROPOSAL_NOT_FOUND,
      fallbackMessage: "Proposal not found or not active",
    })
  );
}

export function throwOnlySellerCanAcceptProposal(): never {
  throw new HttpsError(
    "permission-denied",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.ONLY_SELLER_CAN_ACCEPT_PROPOSAL,
      fallbackMessage: "Only the seller can accept proposals",
    })
  );
}

export function throwProposalInternalError(message?: string): never {
  throw new HttpsError(
    "internal",
    JSON.stringify({
      errorKey: PROPOSAL_ERRORS.INTERNAL_ERROR,
      fallbackMessage: message ?? "Server error in proposal operation",
    })
  );
}
