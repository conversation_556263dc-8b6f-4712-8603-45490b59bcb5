import * as admin from "firebase-admin";
import { OrderEntity, OrderStatus, UserType } from "../../marketplace-shared";
import { refundProposals } from "../../proposal-functions/proposal-service";
import { DBOrdersCollection } from "../../services/db.service";
import { createOrderForMarketCollection } from "../../services/order-service/order-creation-service";
import {
  throwInvalidOrderStatus,
  throwNotOrderBuyer,
  throwOrderNotFound,
} from "./fulfill-and-resell.validator";

export interface FulfillAndResellParams {
  orderId: string;
  price: number;
  userId: string;
}

export interface FulfillAndResellResult {
  success: boolean;
  message: string;
  originalOrderId: string;
  newOrderId: string;
  price: number;
  lockAmount: number;
}

export async function fulfillAndResellOrder({
  orderId,
  price,
  userId,
}: FulfillAndResellParams): Promise<FulfillAndResellResult> {
  // Get the order
  const orderDoc = await DBOrdersCollection.doc(orderId).get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  // Validate that the current user is the buyer of this order
  if (order.buyerId !== userId) {
    throwNotOrderBuyer();
  }

  // Validate that the order status is "gift_sent_to_relayer"
  if (order.status !== OrderStatus.GIFT_SENT_TO_RELAYER) {
    throwInvalidOrderStatus();
  }

  // Parallelize proposal refunding and order fulfillment
  const [, newOrderResult] = await Promise.all([
    // Step 1: Refund all active proposals before fulfilling the order
    refundProposals(orderId),
    // Step 2: Update the original order status to fulfilled and create new order
    Promise.resolve().then(async () => {
      await DBOrdersCollection.doc(orderId).update({
        status: OrderStatus.FULFILLED,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      // Step 3: Create a new order where the current user is the seller
      return createOrderForMarketCollection({
        userId,
        collectionId: order.collectionId,
        price,
        giftId: order.giftId as string,
        userType: UserType.SELLER,
        secondaryMarketPrice: null,
      });
    }),
  ]);

  return {
    success: true,
    message:
      "Order fulfilled and resell order created successfully. No collateral required since gift is in relayer.",
    originalOrderId: orderId,
    newOrderId: newOrderResult.orderId,
    price,
    lockAmount: 0, // No lock amount since no collateral required
  };
}
