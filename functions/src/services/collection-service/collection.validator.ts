import { HttpsError } from "firebase-functions/v2/https";
import { ORDER_ERRORS } from "../../error-messages";
import {
  CollectionEntity,
  CollectionStatus,
  UserType,
} from "../../marketplace-shared";
import { DBCollectionsCollection } from "../db.service";

export async function validateCollectionExists(
  collectionId: string
): Promise<CollectionEntity> {
  const collectionDoc = await DBCollectionsCollection.doc(collectionId).get();

  if (!collectionDoc.exists) {
    throw new HttpsError(
      "not-found",
      JSON.stringify({
        errorKey: ORDER_ERRORS.COLLECTION_NOT_FOUND,
        fallbackMessage: "Collection not found.",
      })
    );
  }

  const collection = collectionDoc.data();
  if (!collection) {
    throw new HttpsError(
      "not-found",
      JSON.stringify({
        errorKey: ORDER_ERRORS.COLLECTION_NOT_FOUND,
        fallbackMessage: "Collection not found.",
      })
    );
  }

  return { ...collection, id: collectionDoc.id } as CollectionEntity;
}

export function validateCollectionIsActive(collection: CollectionEntity): void {
  if (!collection.active) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.COLLECTION_NOT_ACTIVE,
        fallbackMessage: "This collection is not active for order creation.",
      })
    );
  }
}


export function validateFloorPrice(
  amount: number,
  collection: CollectionEntity
): void {
  if (amount < collection.floorPrice) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: ORDER_ERRORS.AMOUNT_BELOW_FLOOR_PRICE,
        params: { floorPrice: collection.floorPrice.toString() },
        fallbackMessage: `Order amount must be at least ${collection.floorPrice} TON (collection floor price).`,
      })
    );
  }
}

export function validateBuyerMarketCollectionRestriction(
  collection: CollectionEntity,
  userType: UserType
): void {
  if (
    userType === UserType.BUYER &&
    collection.status === CollectionStatus.MARKET
  ) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: ORDER_ERRORS.BUYERS_CANNOT_CREATE_MARKET_ORDERS,
        fallbackMessage:
          "Buyers cannot create orders for market collections. Only sellers can create orders for market collections.",
      })
    );
  }
}

export async function validateCollectionAndFloorPrice(params: {
  collectionId: string;
  amount: number;
}): Promise<CollectionEntity> {
  const { collectionId, amount } = params;

  const collection = await validateCollectionExists(collectionId);
  validateCollectionIsActive(collection);
  validateFloorPrice(amount, collection);

  return collection;
}
