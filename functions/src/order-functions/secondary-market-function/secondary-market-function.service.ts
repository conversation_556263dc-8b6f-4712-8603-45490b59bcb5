import * as admin from "firebase-admin";
import {
  OrderEntity,
  RESELL_TX_HISTORY_COLLECTION_NAME,
  ResellTxHistoryEntity,
} from "../../marketplace-shared";
import { refundProposals } from "../../proposal-functions/proposal-service";
import { updateUserBalance } from "../../services/balance-service/balance-service";
import { validateSufficientBalance } from "../../services/financial.validator";
import { DBOrdersCollection } from "../../services/db.service";
import {
  applyResellPurchaseFee,
  calculateFeeAmount,
} from "../../services/fee-service/fee-service";
import { bpsToDecimal, safeMultiply, safeSubtract } from "../../utils";
import {
  validateSetSecondaryPriceRequest,
  validateSecondaryPurchaseRequest,
} from "./secondary-market-function.validator";
import {
  validateOrderExists,
  validateSecondaryMarketAvailability,
  validateSecondaryMarketOrderStatus,
  validateOrderParticipants,
} from "../../services/order-service/order-validation-service";
import { validateUserExists } from "../../services/auth-service/auth.validator";

interface PaymentProcessResult {
  oldBuyerLockedAmount: number;
  feeResult: {
    totalFee: number;
    [key: string]: any;
  };
  netAmountToOldBuyer: number;
}

export interface SetSecondaryPriceParams {
  orderId: string;
  secondaryMarketPrice: number;
  userId: string;
}

export interface SetSecondaryPriceResult {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
}

export interface SecondaryPurchaseParams {
  orderId: string;
  newBuyerId: string;
}

export interface SecondaryPurchaseResult {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
  newBuyerId: string;
  oldBuyerId: string;
  netAmountToOldBuyer: number;
  feeAmount: number;
  lockedAmount: number;
}

export async function setSecondaryMarketPrice({
  orderId,
  secondaryMarketPrice,
  userId,
}: SetSecondaryPriceParams): Promise<SetSecondaryPriceResult> {
  const order = await validateOrderExists(orderId);

  await validateSetSecondaryPriceRequest(
    orderId,
    secondaryMarketPrice,
    userId,
    order
  );

  await updateSecondaryMarketPrice(orderId, secondaryMarketPrice);

  return {
    success: true,
    message: `Secondary market price set to ${secondaryMarketPrice} TON.`,
    orderId,
    secondaryMarketPrice,
  };
}

async function updateSecondaryMarketPrice(
  orderId: string,
  secondaryMarketPrice: number
): Promise<void> {
  await DBOrdersCollection.doc(orderId).update({
    secondaryMarketPrice,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });
}

export async function makeSecondaryMarketPurchase({
  orderId,
  newBuyerId,
}: SecondaryPurchaseParams): Promise<SecondaryPurchaseResult> {
  const [, order] = await Promise.all([
    refundProposals(orderId),
    validateOrderExists(orderId),
    validateUserExists(newBuyerId),
  ]);

  validateSecondaryPurchaseRequest(orderId, newBuyerId, order);
  validateSecondaryMarketAvailability(order);
  validateSecondaryMarketOrderStatus(order);
  validateOrderParticipants(order);

  const secondaryMarketPrice = order.secondaryMarketPrice!;
  const oldBuyerId = order.buyerId!;

  const { oldBuyerLockedAmount, feeResult, netAmountToOldBuyer } =
    await processSecondaryMarketPayment(
      order,
      newBuyerId,
      secondaryMarketPrice
    );

  const newResellerEarnings = await calculateResellerEarnings(
    order,
    secondaryMarketPrice
  );

  await updateOrderForSecondaryPurchase(
    orderId,
    newBuyerId,
    newResellerEarnings
  );

  await createResellTransactionHistory(
    orderId,
    secondaryMarketPrice,
    oldBuyerId,
    newBuyerId
  );

  return {
    success: true,
    message: `Secondary market purchase successful! Paid ${secondaryMarketPrice} TON (${feeResult.totalFee} TON fee). ${oldBuyerLockedAmount} TON locked.`,
    orderId,
    secondaryMarketPrice,
    newBuyerId,
    oldBuyerId,
    netAmountToOldBuyer,
    feeAmount: feeResult.totalFee,
    lockedAmount: oldBuyerLockedAmount,
  };
}

// Helper functions for secondary market purchase
async function processSecondaryMarketPayment(
  order: OrderEntity,
  newBuyerId: string,
  secondaryMarketPrice: number
): Promise<PaymentProcessResult> {
  const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage || 0;
  const buyerLockPercentage = bpsToDecimal(buyerLockPercentageBPS);
  const oldBuyerLockedAmount = safeMultiply(order.price, buyerLockPercentage);

  const resellPurchaseFeeBPS = order.fees?.resell_purchase_fee || 0;
  const feeResult = await applyResellPurchaseFee({
    buyerId: newBuyerId,
    amount: secondaryMarketPrice,
    resellPurchaseFeeBPS,
  });

  const netAmountToOldBuyer = safeSubtract(
    secondaryMarketPrice,
    feeResult.totalFee
  );

  await validateSufficientBalance(
    newBuyerId,
    secondaryMarketPrice,
    "secondary market purchase"
  );

  const oldBuyerId = order.buyerId!;

  // New buyer pays secondary market price and gets locked amount from old buyer
  await updateUserBalance({
    userId: newBuyerId,
    sumChange: -secondaryMarketPrice + oldBuyerLockedAmount,
    lockedChange: oldBuyerLockedAmount,
  });

  // Old buyer receives net amount and loses their locked amount
  await updateUserBalance({
    userId: oldBuyerId,
    sumChange: netAmountToOldBuyer - oldBuyerLockedAmount,
    lockedChange: -oldBuyerLockedAmount,
  });

  return { oldBuyerLockedAmount, feeResult, netAmountToOldBuyer };
}

async function calculateResellerEarnings(
  order: OrderEntity,
  secondaryMarketPrice: number
): Promise<number> {
  const resellPurchaseFeeForSellerBPS =
    order.fees?.resell_purchase_fee_for_seller || 0;
  const resellerEarningsForSeller =
    resellPurchaseFeeForSellerBPS > 0
      ? calculateFeeAmount(secondaryMarketPrice, resellPurchaseFeeForSellerBPS)
      : 0;

  const currentResellerEarnings = order.reseller_earnings_for_seller ?? 0;
  return currentResellerEarnings + resellerEarningsForSeller;
}

async function updateOrderForSecondaryPurchase(
  orderId: string,
  newBuyerId: string,
  newResellerEarnings: number
): Promise<void> {
  await DBOrdersCollection.doc(orderId).update({
    buyerId: newBuyerId,
    secondaryMarketPrice: null,
    reseller_earnings_for_seller: newResellerEarnings,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });
}

async function createResellTransactionHistory(
  orderId: string,
  secondaryMarketPrice: number,
  oldBuyerId: string,
  newBuyerId: string
): Promise<void> {
  const resellTxHistory: ResellTxHistoryEntity = {
    order_id: orderId,
    execution_price: secondaryMarketPrice.toString(),
    reseller_id: oldBuyerId,
    executed_at: admin.firestore.FieldValue.serverTimestamp() as any,
    buyer_id: newBuyerId,
  };

  await DBOrdersCollection.doc(orderId)
    .collection(RESELL_TX_HISTORY_COLLECTION_NAME)
    .add(resellTxHistory);
}
