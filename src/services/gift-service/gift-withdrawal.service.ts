import {
  AppDate,
  GiftEntity,
  GiftStatus,
  OrderEntity,
  OrderStatus,
  TxType,
} from "../../marketplace-shared";
import { log } from "../../utils/logger";
import { db, DBOrdersCollection } from "../../firebase/firebase-admin";
import { getAppConfig } from "../shared/app-config.service";
import {
  getUserBalance,
  spendFundsWithHistory,
} from "../user-service/user-balance.service";

import {
  validateGiftExists,
  validateGiftForWithdrawal,
  validateGiftWithdrawalEligibility,
} from "./gift.validator";
import { validateTelegramId } from "../user-service/user.validator";
import { resolveUserId, getUserById } from "../user-service/user.service";
import { updateGiftOwnership } from "./gift.service";

export interface WithdrawGiftParams {
  giftId: string;
  userTgId: string;
}

export async function withdrawGift(params: WithdrawGiftParams) {
  const { giftId, userTgId } = params;

  try {
    validateTelegramId(userTgId);

    log.info("Processing gift withdrawal", {
      operation: "withdraw_gift_for_bot",
      giftId,
      userTgId,
    });

    // Validate gift exists
    const giftValidation = await validateGiftExists(giftId);
    if (!giftValidation.isValid) {
      return {
        success: false,
        error: giftValidation.error || "Gift validation failed",
      };
    }

    const gift = giftValidation.gift!;

    // Validate gift for withdrawal (ownership and status)
    const withdrawalValidation = validateGiftForWithdrawal(gift, userTgId);
    if (!withdrawalValidation.isValid) {
      return {
        success: false,
        error:
          withdrawalValidation.error || "Gift withdrawal validation failed",
      };
    }

    // Get user data
    const userLookupResult = await resolveUserId({ tgId: userTgId });
    if (!userLookupResult.success || !userLookupResult.userId) {
      return {
        success: false,
        error: "User not found",
      };
    }

    const user = await getUserById(userLookupResult.userId);
    if (!user) {
      return {
        success: false,
        error: "User not found",
      };
    }

    // Validate gift withdrawal eligibility
    const eligibilityValidation = await validateGiftWithdrawalEligibility(
      giftId,
      user.id
    );
    if (!eligibilityValidation.isValid) {
      return {
        success: false,
        error:
          eligibilityValidation.error ||
          "Gift withdrawal eligibility validation failed",
      };
    }

    // Get orders for processing withdrawal
    const ordersQuery = await DBOrdersCollection.where(
      "giftId",
      "==",
      giftId
    ).get();

    if (ordersQuery.empty) {
      // Case 3: Gift not linked to any order
      return await handleWithdrawalWithFee(
        gift,
        user.id,
        "unlinked gift",
        gift.owned_gift_id
      );
    }

    const orders = ordersQuery.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as OrderEntity[];

    // Check for withdrawal-eligible conditions
    for (const order of orders) {
      // Case 1: Order with status "gift_sent_to_relayer" and user is buyer
      if (
        order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
        order.buyerId === user.id
      ) {
        return await handleBuyerWithdrawalFromActiveOrder(
          order,
          gift.owned_gift_id
        );
      }

      // Case 2: Order with status "cancelled" and user is seller
      if (
        order.status === OrderStatus.CANCELLED &&
        order.sellerId === user.id
      ) {
        return await handleWithdrawalWithFee(
          gift,
          user.id,
          "cancelled order",
          gift.owned_gift_id
        );
      }
    }

    return {
      success: false,
      error: "Gift is not eligible for withdrawal under current conditions",
    };
  } catch (error) {
    log.error("Failed to withdraw gift", error, {
      operation: "withdraw_gift_for_bot",
      giftId,
      userTgId,
    });
    throw error;
  }
}

async function handleBuyerWithdrawalFromActiveOrder(
  order: OrderEntity,
  ownedGiftId: string
) {
  try {
    // Update order status to fulfilled
    await DBOrdersCollection.doc(order.id!).update({
      status: OrderStatus.FULFILLED,
      updatedAt: new Date() as AppDate,
    });

    // Transfer gift ownership to buyer (mark as withdrawn)
    if (order.giftId && order.buyerId) {
      const buyer = await getUserById(order.buyerId);
      if (buyer?.tg_id) {
        await updateGiftOwnership(
          order.giftId,
          buyer.tg_id,
          GiftStatus.WITHDRAWN
        );
      }
    }

    log.info("Buyer withdrawal from active order completed", {
      operation: "handle_buyer_withdrawal_from_active_order",
      orderId: order.id,
      ownedGiftId,
    });

    return {
      success: true,
      message: "Gift withdrawn successfully (purchase completed)",
      ownedGiftId,
    };
  } catch (error) {
    log.error("Error in handleBuyerWithdrawalFromActiveOrder", error, {
      operation: "handle_buyer_withdrawal_from_active_order",
      orderId: order.id,
    });
    return {
      success: false,
      error: "Failed to process buyer withdrawal",
    };
  }
}

async function handleWithdrawalWithFee(
  giftDoc: GiftEntity,
  userId: string,
  withdrawalType: string,
  ownedGiftId: string
) {
  try {
    const config = await getAppConfig();
    const withdrawalFee = config?.withdrawal_fee || 0;

    // Check if user has enough balance for fee
    const userBalance = await getUserBalance(userId);
    const availableBalance = userBalance.sum - userBalance.locked;

    if (availableBalance < withdrawalFee) {
      return {
        success: false,
        error: "Insufficient balance to cover withdrawal fee",
      };
    }

    const batch = db.batch();

    // Deduct withdrawal fee from user balance
    if (withdrawalFee > 0) {
      await spendFundsWithHistory({
        userId: userId,
        amount: withdrawalFee,
        txType: TxType.CANCELATION_FEE,
        description: `Gift withdrawal fee - ${withdrawalType}`,
        descriptionIntlKey: "transaction.withdrawal_fee",
        descriptionIntlParams: { type: withdrawalType },
      });
    }

    const giftRef = db.collection("gifts").doc(giftDoc.id as string);
    batch.update(giftRef, {
      status: GiftStatus.WITHDRAWN,
      updatedAt: new Date() as AppDate,
    });

    await batch.commit();

    log.info("Withdrawal with fee completed", {
      operation: "handle_withdrawal_with_fee",
      userId,
      withdrawalType,
      withdrawalFee,
      ownedGiftId,
    });

    return {
      success: true,
      message: `Gift withdrawn successfully (${withdrawalType})`,
      ownedGiftId,
    };
  } catch (error) {
    log.error("Error in handleWithdrawalWithFee", error, {
      operation: "handle_withdrawal_with_fee",
      userId,
      withdrawalType,
    });
    return {
      success: false,
      error: "Failed to process withdrawal with fee",
    };
  }
}
