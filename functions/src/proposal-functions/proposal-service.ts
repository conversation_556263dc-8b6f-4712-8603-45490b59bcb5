import * as admin from "firebase-admin";
import { TRANSACTION_DESCRIPTION_INTL_KEYS } from "../transaction-description-intl-keys";
import {
  OrderEntity,
  OrderStatus,
  PROPOSAL_PRICES_SUBCOLLECTION,
  ProposalEntity,
  ProposalStatus,
  TxType,
} from "../marketplace-shared";
import {
  addFundsWithHistory,
  lockFundsWithHistory,
  spendLockedFunds,
  unlockFundsWithHistory,
} from "../services/balance-service/balance-service";
import { db, DBOrdersCollection } from "../services/db.service";
import { getAppConfig } from "../services/fee-service/fee-service";
import {
  calculateSellerCollateralAdjustment,
  getOrderById,
} from "../services/order-service/order-service";
import { createTransactionRecord } from "../services/transaction-history-service/transaction-history-service";
import { validateUserExists } from "../services/auth-service/auth.validator";
import {
  throwNoActiveProposalFound,
  throwOnlySellerCanAcceptProposal,
  throwOrderNotFound,
  throwProposalInternalError,
  throwProposalNotFound,
  throwProposalOnlyOnSellOrders,
} from "./proposal-function.validator";
import { ProposalLogger } from "./proposal-function.logger";
import {
  notifyProposerAccepted,
  notifySellerNewProposal,
} from "./proposal-notification-service";

export interface ProposeOrderPriceResult {
  success: boolean;
  message: string;
  proposalId?: string;
}

export interface CancelProposalResult {
  success: boolean;
  message: string;
  refundAmount?: number;
  feeAmount?: number;
}

export interface AcceptProposalResult {
  success: boolean;
  message: string;
  newOrderPrice?: number;
}

export async function proposeOrderPrice(
  orderId: string,
  proposerId: string,
  proposedPrice: number
): Promise<ProposeOrderPriceResult> {
  try {
    // Step 1: Validate proposal creation
    const validation = await validateProposalCreation(
      orderId,
      proposerId,
      proposedPrice
    );
    if (!validation.isValid) {
      ProposalLogger.logProposalValidationFailed({
        orderId,
        proposerId,
        proposedPrice,
        reason: validation.error || "Proposal validation failed",
      });
      throwProposalInternalError(
        validation.error || "Proposal validation failed"
      );
    }

    const order = validation.order!;

    // Step 2: Lock proposer's collateral
    await lockFundsWithHistory({
      userId: proposerId,
      amount: proposedPrice,
      txType: TxType.PROPOSAL_COLLATERAL_LOCK,
      orderId,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.PROPOSAL_COLLATERAL_LOCK,
      descriptionIntlParams: {
        amount: proposedPrice.toString(),
        orderId,
      },
    });

    // Step 3: Create proposal document
    const proposalData: Omit<ProposalEntity, "id"> = {
      proposer_id: proposerId,
      proposed_price: proposedPrice,
      status: ProposalStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const proposalRef = await DBOrdersCollection.doc(orderId)
      .collection(PROPOSAL_PRICES_SUBCOLLECTION)
      .add(proposalData);

    // Send notification to seller (no need to await)
    notifySellerNewProposal({
      orderId,
      sellerId: order.sellerId!,
      proposedPrice,
      originalPrice: order.price,
      orderNumber: order.number,
    });

    ProposalLogger.logProposalCreated({
      orderId,
      proposerId,
      proposedPrice,
      originalPrice: order.price,
      collateralAmount: proposedPrice,
    });

    return {
      success: true,
      message: "Proposal created successfully",
      proposalId: proposalRef.id,
    };
  } catch (error) {
    console.error("Error creating proposal:", error);
    return {
      success: false,
      message: "Failed to create proposal",
    };
  }
}

export async function cancelProposal(
  orderId: string,
  proposerId: string
): Promise<CancelProposalResult> {
  try {
    // Step 1: Validate this is a sell order
    const order = await getOrderById(orderId);
    if (!order) {
      throwOrderNotFound();
    }

    if (!order.sellerId || order.buyerId) {
      throwProposalOnlyOnSellOrders();
    }

    // Step 2: Get user's active proposal
    const proposal = await getUserActiveProposal(orderId, proposerId);

    if (!proposal) {
      throwNoActiveProposalFound();
    }

    // Step 2: Cancel the proposal
    await DBOrdersCollection.doc(orderId)
      .collection(PROPOSAL_PRICES_SUBCOLLECTION)
      .doc(proposal.id!)
      .update({
        status: ProposalStatus.CANCELLED,
        updatedAt: new Date(),
      });

    // Step 3: Get cancellation fee from app config
    const appConfig = await getAppConfig();
    const cancellationFee = appConfig.cancel_price_proposal_fee || 0;

    // Step 4: Unlock funds with fee deduction
    const refundAmount = proposal.proposed_price - cancellationFee;

    await unlockFundsWithHistory({
      userId: proposerId,
      amount: refundAmount,
      txType: TxType.PROPOSAL_COLLATERAL_UNLOCK,
      orderId,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.PROPOSAL_COLLATERAL_REFUND,
      descriptionIntlParams: {
        amount: refundAmount.toString(),
        orderId,
      },
    });

    // Step 5: Apply cancellation fee if applicable
    if (cancellationFee > 0) {
      await spendLockedFunds(proposerId, cancellationFee);

      // Create transaction record for the fee
      await createTransactionRecord({
        userId: proposerId,
        amount: cancellationFee,
        txType: TxType.CANCELATION_FEE,
        orderId,
        descriptionIntlKey:
          TRANSACTION_DESCRIPTION_INTL_KEYS.PROPOSAL_CANCELLATION_FEE,
        descriptionIntlParams: {
          amount: cancellationFee.toString(),
          orderId,
        },
      });
    }

    return {
      success: true,
      message: "Proposal cancelled successfully",
      refundAmount,
      feeAmount: cancellationFee,
    };
  } catch (error) {
    console.error("Error cancelling proposal:", error);
    return {
      success: false,
      message: "Failed to cancel proposal",
    };
  }
}

export async function acceptProposal(
  orderId: string,
  proposalId: string,
  sellerId: string
): Promise<AcceptProposalResult> {
  try {
    // Step 1: Validate seller and get proposal
    const order = await getOrderById(orderId);
    if (!order) {
      throwOrderNotFound();
    }

    // Validate this is a sell order
    if (!order.sellerId || order.buyerId) {
      throwProposalOnlyOnSellOrders();
    }

    // Validate seller ownership
    if (order.sellerId !== sellerId) {
      throwOnlySellerCanAcceptProposal();
    }

    const proposal = await getProposalById(orderId, proposalId);
    if (!proposal || proposal.status !== ProposalStatus.ACTIVE) {
      throwProposalNotFound();
    }

    // Step 2: Accept the proposal and update order price
    await db.runTransaction(async (transaction) => {
      // Update proposal status
      const proposalRef = DBOrdersCollection.doc(orderId)
        .collection(PROPOSAL_PRICES_SUBCOLLECTION)
        .doc(proposalId);

      transaction.update(proposalRef, {
        status: ProposalStatus.ACCEPTED,
        updatedAt: new Date(),
      });

      // Update order price and handle seller collateral adjustment for PREMARKET collections
      const orderRef = DBOrdersCollection.doc(orderId);
      transaction.update(orderRef, {
        price: proposal.proposed_price,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      // For PREMARKET collections, calculate seller collateral adjustment
      if (
        order.fees?.seller_locked_percentage &&
        order.fees.seller_locked_percentage > 0
      ) {
        const collateralAdjustment = calculateSellerCollateralAdjustment(
          order.price,
          proposal.proposed_price,
          order.fees.seller_locked_percentage
        );

        if (collateralAdjustment.refundAmount > 0) {
          // Refund the difference to seller
          await addFundsWithHistory({
            userId: order.sellerId!,
            amount: collateralAdjustment.refundAmount,
            txType: TxType.PROPOSAL_COLLATERAL_REFUND,
            orderId,
            descriptionIntlKey:
              TRANSACTION_DESCRIPTION_INTL_KEYS.PROPOSAL_COLLATERAL_REFUND,
            descriptionIntlParams: {
              amount: collateralAdjustment.refundAmount.toString(),
              orderId,
            },
          });

          ProposalLogger.logSellerCollateralAdjustment({
            orderId,
            sellerId: order.sellerId!,
            oldCollateral: collateralAdjustment.oldCollateral,
            newCollateral: collateralAdjustment.newCollateral,
            refundAmount: collateralAdjustment.refundAmount,
          });
        }
      }
    });

    // Step 3: Refund all other active proposals
    await refundProposals(orderId, proposalId);

    // Step 4: Notify proposer (no need to await)
    notifyProposerAccepted({
      orderId,
      proposerId: proposal.proposer_id,
      proposedPrice: proposal.proposed_price,
      orderNumber: order.number,
    });

    ProposalLogger.logProposalAccepted({
      orderId,
      proposalId,
      sellerId,
      proposerId: proposal.proposer_id,
      proposedPrice: proposal.proposed_price,
      originalPrice: order.price,
      newOrderPrice: proposal.proposed_price,
    });

    return {
      success: true,
      message: "Proposal accepted successfully",
      newOrderPrice: proposal.proposed_price,
    };
  } catch (error) {
    console.error("Error accepting proposal:", error);
    throwProposalInternalError("Failed to accept proposal");
  }
}

async function validateProposalCreation(
  orderId: string,
  proposerId: string,
  proposedPrice: number
): Promise<{ isValid: boolean; error?: string; order?: OrderEntity }> {
  // Get order
  const order = await getOrderById(orderId);
  if (!order) {
    return { isValid: false, error: "Order not found" };
  }

  // Validate order status
  if (order.status !== OrderStatus.ACTIVE) {
    return { isValid: false, error: "Order must be active to propose price" };
  }

  // Validate this is a sell order (has sellerId but no buyerId)
  if (!order.sellerId || order.buyerId) {
    return {
      isValid: false,
      error: "Proposals can only be made on sell orders",
    };
  }

  // Validate proposer is not the seller
  if (order.sellerId === proposerId) {
    return {
      isValid: false,
      error: "Seller cannot propose on their own order",
    };
  }

  // Validate order doesn't have secondary market price
  if (order.secondaryMarketPrice && order.secondaryMarketPrice > 0) {
    return {
      isValid: false,
      error: "Cannot propose on orders with secondary market price",
    };
  }

  // Validate proposed price
  if (proposedPrice <= 0 || proposedPrice >= order.price) {
    return {
      isValid: false,
      error:
        "Proposed price must be positive and less than current order price",
    };
  }

  // Check if proposed price is lower than existing proposals
  const minimumExistingPrice = await getMinimumProposalPrice(orderId);
  if (minimumExistingPrice !== null && proposedPrice >= minimumExistingPrice) {
    return {
      isValid: false,
      error: "Proposed price must be lower than existing proposals",
    };
  }

  // Check if user already has an active proposal
  const existingProposal = await getUserActiveProposal(orderId, proposerId);
  if (existingProposal) {
    return {
      isValid: false,
      error: "User already has an active proposal for this order",
    };
  }

  // Check user balance
  const user = await validateUserExists(proposerId);
  if (!user?.balance || user.balance.sum < proposedPrice) {
    return {
      isValid: false,
      error: "Insufficient balance to create proposal",
    };
  }

  return { isValid: true, order };
}

async function getProposalById(
  orderId: string,
  proposalId: string
): Promise<ProposalEntity | null> {
  const proposalDoc = await DBOrdersCollection.doc(orderId)
    .collection(PROPOSAL_PRICES_SUBCOLLECTION)
    .doc(proposalId)
    .get();
  return proposalDoc.exists
    ? ({ id: proposalDoc.id, ...proposalDoc.data() } as ProposalEntity)
    : null;
}

async function getUserActiveProposal(
  orderId: string,
  proposerId: string
): Promise<ProposalEntity | null> {
  const proposalQuery = await DBOrdersCollection.doc(orderId)
    .collection(PROPOSAL_PRICES_SUBCOLLECTION)
    .where("proposer_id", "==", proposerId)
    .where("status", "==", ProposalStatus.ACTIVE)
    .get();

  return proposalQuery.empty
    ? null
    : ({
        id: proposalQuery.docs[0].id,
        ...proposalQuery.docs[0].data(),
      } as ProposalEntity);
}

async function getMinimumProposalPrice(
  orderId: string
): Promise<number | null> {
  const proposalQuery = await DBOrdersCollection.doc(orderId)
    .collection(PROPOSAL_PRICES_SUBCOLLECTION)
    .where("status", "==", ProposalStatus.ACTIVE)
    .orderBy("proposed_price", "asc")
    .limit(1)
    .get();

  return proposalQuery.empty
    ? null
    : (proposalQuery.docs[0].data() as ProposalEntity).proposed_price;
}

export async function refundProposals(
  orderId: string,
  excludeProposalId?: string
): Promise<void> {
  const proposalsQuery = await DBOrdersCollection.doc(orderId)
    .collection(PROPOSAL_PRICES_SUBCOLLECTION)
    .where("status", "==", ProposalStatus.ACTIVE)
    .get();

  if (proposalsQuery.empty) {
    return;
  }

  const batch = db.batch();
  const refundOperations: Array<Promise<void>> = [];

  for (const proposalDoc of proposalsQuery.docs) {
    if (excludeProposalId && proposalDoc.id === excludeProposalId) {
      continue;
    }

    const proposal = proposalDoc.data() as ProposalEntity;

    // Update proposal status to cancelled
    batch.update(proposalDoc.ref, {
      status: ProposalStatus.CANCELLED,
      updatedAt: new Date(),
    });

    // Batch the refund operations to run in parallel
    refundOperations.push(
      (async () => {
        // Create transaction record for refund
        await createTransactionRecord({
          userId: proposal.proposer_id,
          amount: proposal.proposed_price,
          txType: TxType.PROPOSAL_COLLATERAL_REFUND,
          orderId,
          descriptionIntlKey:
            TRANSACTION_DESCRIPTION_INTL_KEYS.PROPOSAL_COLLATERAL_REFUND,
          descriptionIntlParams: {
            amount: proposal.proposed_price.toString(),
            orderId,
          },
        });

        // Unlock the proposer's funds
        await unlockFundsWithHistory({
          userId: proposal.proposer_id,
          amount: proposal.proposed_price,
          txType: TxType.PROPOSAL_COLLATERAL_REFUND,
          orderId,
          descriptionIntlKey:
            TRANSACTION_DESCRIPTION_INTL_KEYS.PROPOSAL_COLLATERAL_REFUND,
          descriptionIntlParams: {
            amount: proposal.proposed_price.toString(),
            orderId,
          },
        });

        ProposalLogger.logCollateralRefund({
          orderId,
          proposerId: proposal.proposer_id,
          refundAmount: proposal.proposed_price,
          reason: excludeProposalId
            ? "Other proposal accepted"
            : "Order cancelled",
        });
      })()
    );
  }

  // Execute batch update and refund operations in parallel
  await Promise.all([batch.commit(), ...refundOperations]);
}
