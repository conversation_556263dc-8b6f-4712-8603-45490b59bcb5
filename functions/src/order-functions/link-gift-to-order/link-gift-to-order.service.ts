import {
  formatDateToFirebaseTimestamp,
  OrderEntity,
  OrderStatus,
} from "../../marketplace-shared";
import { validateGiftForLinking } from "../../services/gift-service/gift-validation.service";
import { validateUserExists } from "../../services/auth-service/auth.validator";
import {
  throwGiftCollectionMismatch,
  throwGiftNotOwnedByUser,
  throwInvalidOrderStatus,
  throwOrderNotFound,
  throwOrderNotOwnedByUser,
} from "./link-gift-to-order.validator";
import { LinkGiftToOrderLogger } from "./link-gift-to-order.logger";

import {
  db,
  DBOrdersCollection,
  getCurrentDateFirestoreTimestamp,
} from "../../services/db.service";
import { processPaidOrderWithGift } from "../../services/order-service/order-gift-processing.service";

export interface LinkGiftToOrderParams {
  giftId: string;
  orderId: string;
  userId: string;
}

export async function linkGiftToOrderService({
  giftId,
  orderId,
  userId,
}: LinkGiftToOrderParams) {
  // Parallelize gift validation and order fetching
  const [gift, orderDoc] = await Promise.all([
    validateGiftForLinking(giftId),
    DBOrdersCollection.doc(orderId).get(),
  ]);

  LinkGiftToOrderLogger.logGiftValidation({
    giftId,
    giftOwnerId: gift.owner_tg_id,
    giftCollectionId: gift.collectionId,
  });

  // Validate order exists
  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  // Get user for validation
  const user = await validateUserExists(userId);
  if (!user || user.tg_id !== gift.owner_tg_id) {
    throwGiftNotOwnedByUser();
  }

  if (!order.sellerId) {
    throw new Error("Order has no seller ID");
  }
  LinkGiftToOrderLogger.logOrderValidation({
    orderId,
    orderSellerId: order.sellerId,
    orderStatus: order.status,
    orderCollectionId: order.collectionId,
    userId,
  });

  // 4. Validate user is the seller of the order
  if (order.sellerId !== userId) {
    throwOrderNotOwnedByUser();
  }

  // 5. Validate order status (must be CREATED or PAID)
  if (
    order.status !== OrderStatus.CREATED &&
    order.status !== OrderStatus.PAID
  ) {
    throwInvalidOrderStatus(order.status);
  }

  // 6. Validate gift and order are from the same collection
  if (gift.collectionId !== order.collectionId) {
    throwGiftCollectionMismatch();
  }

  // 7. Link gift to order based on order status
  const batch = db.batch();

  if (order.status === OrderStatus.CREATED) {
    // For CREATED orders, activate the order
    batch.update(orderDoc.ref, {
      giftId,
      status: OrderStatus.ACTIVE,
      updatedAt: formatDateToFirebaseTimestamp(
        getCurrentDateFirestoreTimestamp()
      ),
    });

    await batch.commit();

    LinkGiftToOrderLogger.logGiftLinkedToOrder({
      giftId,
      orderId,
      userId,
      newOrderStatus: OrderStatus.ACTIVE,
    });

    return {
      success: true,
      message: "Gift linked to order and order activated successfully.",
      order: {
        ...order,
        giftId,
        status: OrderStatus.ACTIVE,
      },
    };
  } else {
    // For PAID orders, use reusable function to process paid order with gift
    await processPaidOrderWithGift({
      order,
      giftId,
      orderDoc,
      batch,
    });

    LinkGiftToOrderLogger.logGiftLinkedToOrder({
      giftId,
      orderId,
      userId,
      newOrderStatus: OrderStatus.GIFT_SENT_TO_RELAYER,
    });

    return {
      success: true,
      message: "Gift linked to paid order and sent to relayer successfully.",
      order: {
        ...order,
        giftId,
        status: OrderStatus.GIFT_SENT_TO_RELAYER,
      },
    };
  }
}
