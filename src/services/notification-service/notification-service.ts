import { botMessages } from "../../intl/messages";
import { PREM_RELAYER_USERNAME } from "../../app.constants";
import { sendNotificationToUser } from "../shared/notification-base.service";

export async function notifySellerOrderPaid(params: {
  orderId: string;
  sellerId: string;
  orderNumber?: number;
  price?: number;
}): Promise<{ success: boolean; message: string }> {
  const { orderId, sellerId, orderNumber, price } = params;

  return sendNotificationToUser(
    {
      orderId,
      userId: sellerId,
      ...(orderNumber !== undefined && { orderNumber }),
    },
    {
      messageId: botMessages.sellerOrderPaidWithLink.id,
      params: {
        price: price ? price.toString() : "N/A",
      },
    },
    "seller"
  );
}

export async function notifyBuyerGiftSent(params: {
  orderId: string;
  buyerId: string;
  orderNumber?: number;
}) {
  const { orderId, buyerId, orderNumber } = params;

  return sendNotificationToUser(
    {
      orderId,
      userId: buyerId,
      ...(orderNumber !== undefined && { orderNumber }),
    },
    {
      messageId: botMessages.buyerGiftSentWithLink.id,
      params: {
        relayerUsername: PREM_RELAYER_USERNAME.replace("@", ""),
      },
    },
    "buyer"
  );
}

export async function notifySellerNewProposal(params: {
  orderId: string;
  sellerId: string;
  proposedPrice: number;
  originalPrice: number;
  orderNumber?: number;
}) {
  const { orderId, sellerId, proposedPrice, originalPrice, orderNumber } =
    params;

  return sendNotificationToUser(
    {
      orderId,
      userId: sellerId,
      ...(orderNumber !== undefined && { orderNumber }),
    },
    {
      messageId: botMessages.newProposalReceived.id,
      params: {
        proposedPrice: proposedPrice.toString(),
        originalPrice: originalPrice.toString(),
      },
    },
    "seller"
  );
}

export async function notifyProposerAccepted(params: {
  orderId: string;
  proposerId: string;
  proposedPrice: number;
  orderNumber?: number;
}) {
  const { orderId, proposerId, proposedPrice, orderNumber } = params;

  return sendNotificationToUser(
    {
      orderId,
      userId: proposerId,
      ...(orderNumber !== undefined && { orderNumber }),
    },
    {
      messageId: botMessages.proposalAccepted.id,
      params: {
        proposedPrice: proposedPrice.toString(),
      },
    },
    "buyer"
  );
}
