import * as admin from "firebase-admin";

import {
  AppDate,
  CollectionEntity,
  CollectionStatus,
  OrderEntity,
  OrderStatus,
  TxType,
  UserType,
} from "../../marketplace-shared";
import { TRANSACTION_DESCRIPTION_INTL_KEYS } from "../../transaction-description-intl-keys";
import { lockFundsWithHistory } from "../balance-service/balance-service";
import { getNextCounterValue } from "../counter-service/counter-service";
import { DBCollectionsCollection, DBOrdersCollection } from "../db.service";
import { calculateOrderDeadline } from "../deadline-service/deadline-service";
import { getAppConfig } from "../fee-service/fee-service";
import {
  validateOrderCreation,
  validateOrderCreationForMarketCollection,
} from "./order-validation-service";

async function createFeesSnapshot() {
  const appConfig = await getAppConfig();
  if (!appConfig) {
    throw new Error("App config not found");
  }

  return {
    buyer_locked_percentage: appConfig.buyer_lock_percentage,
    seller_locked_percentage: appConfig.seller_lock_percentage,
    purchase_fee: appConfig.purchase_fee,
    referrer_fee: appConfig.referrer_fee,
    order_cancellation_fee: appConfig.cancel_order_fee,
    resell_purchase_fee: appConfig.resell_purchase_fee,
    resell_purchase_fee_for_seller: appConfig.resell_purchase_fee_for_seller,
  };
}

async function createBaseOrderData(params: {
  collectionId: string;
  price: number;
  giftId: string | null;
  secondaryMarketPrice: number | null;
  status: OrderStatus;
}): Promise<Omit<OrderEntity, "id" | "buyerId" | "sellerId">> {
  const { collectionId, price, giftId, secondaryMarketPrice, status } = params;

  // Parallelize independent database operations
  const [orderNumber, fees, collectionDoc] = await Promise.all([
    getNextCounterValue(),
    createFeesSnapshot(),
    DBCollectionsCollection.doc(collectionId).get(),
  ]);

  const collection = collectionDoc.exists
    ? (collectionDoc.data() as CollectionEntity)
    : null;

  const deadline = await calculateOrderDeadline(collection);

  return {
    number: orderNumber,
    collectionId,
    price,
    status,
    giftId,
    deadline: deadline as AppDate,
    secondaryMarketPrice: secondaryMarketPrice ?? null,
    reseller_earnings_for_seller: 0,
    fees,
    createdAt: admin.firestore.FieldValue.serverTimestamp() as AppDate,
    updatedAt: admin.firestore.FieldValue.serverTimestamp() as AppDate,
  };
}

export async function createOrderForPremarketCollection(params: {
  userId: string;
  collectionId: string;
  price: number;
  giftId: string | null;
  userType: UserType;
  secondaryMarketPrice: number | null;
}) {
  const {
    userId,
    collectionId,
    price,
    userType,
    secondaryMarketPrice,
    giftId,
  } = params;

  const validation = await validateOrderCreation({
    userId,
    collectionId,
    price,
    userType,
  });

  const baseOrderData = await createBaseOrderData({
    collectionId,
    price,
    giftId,
    secondaryMarketPrice,
    status: OrderStatus.ACTIVE,
  });

  const orderData: Omit<OrderEntity, "id"> = {
    ...baseOrderData,
    ...(userType === UserType.BUYER
      ? { buyerId: userId }
      : { sellerId: userId }),
  };

  // Create the order
  const orderRef = await DBOrdersCollection.add(orderData);

  // Lock funds and create transaction history with orderId
  const txType =
    userType === UserType.BUYER
      ? TxType.BUY_LOCK_COLLATERAL
      : TxType.SELL_LOCK_COLLATERAL;

  await lockFundsWithHistory({
    userId,
    amount: validation.lockedAmount,
    txType,
    orderId: orderRef.id,
    descriptionIntlKey:
      userType === "buyer"
        ? TRANSACTION_DESCRIPTION_INTL_KEYS.LOCKED_COLLATERAL_FOR_BUYER
        : TRANSACTION_DESCRIPTION_INTL_KEYS.LOCKED_COLLATERAL_FOR_SELLER,
    descriptionIntlParams: {
      amount: validation.lockedAmount.toString(),
      percentage: (validation.lockPercentage * 100).toString(),
      orderPrice: price.toString(),
    },
  });

  return {
    success: true,
    orderId: orderRef.id,
    message: `Order created successfully with ${
      validation.lockedAmount
    } TON locked (${validation.lockPercentage * 100}% of ${price} TON order)`,
    lockedAmount: validation.lockedAmount,
    lockPercentage: validation.lockPercentage,
  };
}

export async function createOrderForMarketCollection(params: {
  userId: string;
  collectionId: string;
  price: number;
  giftId: string | null;
  userType: UserType;
  secondaryMarketPrice: number | null;
}) {
  const {
    userId,
    collectionId,
    price,
    userType,
    secondaryMarketPrice,
    giftId,
  } = params;

  // Validate collection and floor price, skip balance validation for sellers
  const validation = await validateOrderCreationForMarketCollection({
    userId,
    collectionId,
    price,
    userType,
  });

  const baseOrderData = await createBaseOrderData({
    collectionId,
    price,
    giftId,
    secondaryMarketPrice,
    status: giftId ? OrderStatus.ACTIVE : OrderStatus.CREATED,
  });

  const orderData: Omit<OrderEntity, "id"> = {
    ...baseOrderData,
    ...(userType === UserType.BUYER
      ? { buyerId: userId }
      : { sellerId: userId }),
  };

  // Create the order
  const orderRef = await DBOrdersCollection.add(orderData);

  // Lock buyer funds AFTER order creation to have proper orderId
  if (userType === UserType.BUYER) {
    await lockFundsWithHistory({
      userId,
      amount: validation.lockedAmount,
      txType: TxType.BUY_LOCK_COLLATERAL,
      orderId: orderRef.id,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.LOCKED_COLLATERAL_FOR_BUYER,
      descriptionIntlParams: {
        amount: validation.lockedAmount.toString(),
        percentage: (validation.lockPercentage * 100).toString(),
        orderPrice: price.toString(),
      },
    });
  }

  const message =
    userType === UserType.SELLER
      ? "Order created successfully. No collateral required for MARKET collections."
      : `Order created successfully with ${
          validation.lockedAmount
        } TON locked (${
          validation.lockPercentage * 100
        }% of ${price} TON order)`;

  return {
    success: true,
    orderId: orderRef.id,
    message,
    lockedAmount: userType === UserType.SELLER ? 0 : validation.lockedAmount,
    lockPercentage:
      userType === UserType.SELLER ? 0 : validation.lockPercentage,
  };
}

export async function createOrder(params: {
  userId: string;
  collectionId: string;
  price: number;
  giftId: string | null;
  userType: UserType;
  secondaryMarketPrice: number | null;
}) {
  const { collectionId } = params;

  // Get collection to determine status
  const collectionDoc = await DBCollectionsCollection.doc(collectionId).get();

  if (!collectionDoc.exists) {
    throw new Error("Collection not found");
  }

  const collection = collectionDoc.data();

  let result;
  // Route to appropriate function based on collection status
  if (collection?.status === CollectionStatus.MARKET) {
    result = await createOrderForMarketCollection(params);
  } else {
    // PREMARKET or other statuses use original logic
    result = await createOrderForPremarketCollection(params);
  }

  return result;
}
