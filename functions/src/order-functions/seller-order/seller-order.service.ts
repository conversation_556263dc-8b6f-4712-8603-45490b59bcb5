import { HttpsError } from "firebase-functions/v2/https";
import { GIFT_ERRORS } from "../../error-messages";
import { UserType } from "../../marketplace-shared";
import { refundProposals } from "../../proposal-functions/proposal-service";
import {
  getUserData,
  requireAuthentication,
} from "../../services/auth-service/auth.service";
import {
  requireTelegramId,
  validateSellerOwnership,
  validateUserExists,
} from "../../services/auth-service/auth.validator";
import { validateGiftForLinking } from "../../services/gift-service/gift-validator";
import { createOrder } from "../../services/order-service/order-creation-service";
import { validateSellerCreatedOrdersLimit } from "../../services/order-service/order-validation-service";
import { processPurchase } from "../../services/purchase-service/purchase-service";
import {
  validateCollectionId,
  validateOrderId,
  validatePrice,
  validateUserId,
} from "../../services/request.validator";

export interface CreateOrderAsSellerParams {
  sellerId: string;
  collectionId: string;
  price: number;
  giftId?: string;
}

export interface MakePurchaseAsSellerParams {
  sellerId: string;
  orderId: string;
}

export async function validateCreateOrderRequest(
  request: any,
  params: CreateOrderAsSellerParams
) {
  const authRequest = requireAuthentication(request);
  validateUserId(params.sellerId);
  validateCollectionId(params.collectionId);
  validatePrice(params.price);
  validateSellerOwnership(authRequest, params.sellerId);

  // Validate user has telegram ID for order operations
  const user = await getUserData(authRequest.auth.uid);
  requireTelegramId(user, "create orders");

  return { authRequest, user };
}

export async function validateGiftForOrder(
  giftId: string,
  sellerId: string,
  collectionId: string
) {
  // Parallelize gift validation and user lookup
  const [gift, user] = await Promise.all([
    validateGiftForLinking(giftId),
    validateUserExists(sellerId),
  ]);

  if (!user || user.tg_id !== gift.owner_tg_id) {
    throw new HttpsError(
      "permission-denied",
      JSON.stringify({
        errorKey: GIFT_ERRORS.GIFT_NOT_OWNED_BY_USER,
        fallbackMessage: "Gift does not belong to the seller",
      })
    );
  }

  // Validate gift and order are from the same collection
  if (gift.collectionId !== collectionId) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: GIFT_ERRORS.GIFT_COLLECTION_MISMATCH,
        fallbackMessage: "Gift collection does not match order collection",
      })
    );
  }
}

export async function validatePurchaseRequest(
  request: any,
  params: MakePurchaseAsSellerParams
) {
  const authRequest = requireAuthentication(request);
  validateUserId(params.sellerId);
  validateOrderId(params.orderId);
  validateSellerOwnership(authRequest, params.sellerId);

  // Validate user has telegram ID for purchase operations
  const user = await getUserData(authRequest.auth.uid);
  requireTelegramId(user, "make purchases");

  return { authRequest, user };
}

export async function createSellerOrder(params: CreateOrderAsSellerParams) {
  const { sellerId, collectionId, price, giftId } = params;

  // Parallelize gift validation and order limit validation
  const validationPromises = [
    validateSellerCreatedOrdersLimit(sellerId, collectionId),
  ];

  if (giftId) {
    validationPromises.push(
      validateGiftForOrder(giftId, sellerId, collectionId)
    );
  }

  await Promise.all(validationPromises);

  return createOrder({
    userId: sellerId,
    collectionId,
    price,
    giftId: giftId || null,
    userType: UserType.SELLER,
    secondaryMarketPrice: null,
  });
}

export async function processSellerPurchase(
  params: MakePurchaseAsSellerParams
) {
  const { sellerId, orderId } = params;

  // Parallelize proposal refunding and purchase processing preparation
  await refundProposals(orderId);

  return processPurchase({
    userId: sellerId,
    orderId,
    userType: UserType.SELLER,
  });
}
