import { Context, Middleware } from "telegraf";
import { LanguagePreferenceService } from "../services/shared/language-preference.service";
import { T, TNoContext, SupportedLocale } from "../i18n";
import { botMessages } from "../intl/messages";
import { APP_NAME } from "../app.constants";

export interface LanguageContext extends Context {
  userLanguage: SupportedLocale;
}

export const languageDetectionMiddleware: Middleware<Context> = async (
  ctx,
  next
) => {
  try {
    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      (ctx as any).userLanguage = "en";
      return next();
    }

    // Get user's saved language preference
    const savedLanguage = await LanguagePreferenceService.getUserLanguage(tgId);
    (ctx as any).userLanguage = savedLanguage;

    // Check if this is a start command or first interaction
    const isStartCommand =
      ctx.message &&
      "text" in ctx.message &&
      ctx.message.text?.startsWith("/start");

    // Check if user has any explicit language preference stored
    // If no record exists, this is a first interaction
    const hasExplicitPreference =
      await LanguagePreferenceService.hasExplicitLanguagePreference(tgId);
    const isFirstInteraction = !hasExplicitPreference;

    if (isStartCommand && isFirstInteraction) {
      // Detect language from Telegram client
      const detectedLanguage = detectLanguageFromContext(ctx);

      if (detectedLanguage && detectedLanguage !== "en") {
        await handleLanguageDetection(ctx, detectedLanguage);
        return; // Don't proceed to next middleware, wait for user response
      }
    }

    return next();
  } catch (error) {
    console.error("Language detection middleware error", error);
    (ctx as any).userLanguage = "en";
    return next();
  }
};

function detectLanguageFromContext(ctx: Context): SupportedLocale | null {
  // Check language_code from Telegram user object
  const languageCode = ctx.from?.language_code?.toLowerCase();

  if (!languageCode) {
    return null;
  }

  // Map language codes to supported languages
  if (languageCode === "uk" || languageCode === "ua") {
    return SupportedLocale.UA;
  }

  if (languageCode === "ru" || languageCode === "be") {
    return SupportedLocale.RU;
  }

  return null;
}

async function handleLanguageDetection(
  ctx: Context,
  detectedLanguage: SupportedLocale
): Promise<void> {
  const promptMessages = {
    uk: T(ctx, botMessages.languageDetectionPromptUkrainian.id),
    ru: T(ctx, botMessages.languageDetectionPromptRussian.id),
  };

  const keepEnglishText = T(ctx, botMessages.languageKeepEnglish.id);
  const switchTexts = {
    uk: T(ctx, botMessages.languageSwitchToUkrainian.id),
    ru: T(ctx, botMessages.languageSwitchToRussian.id),
  };

  await ctx.reply(
    promptMessages[detectedLanguage as keyof typeof promptMessages],
    {
      reply_markup: {
        inline_keyboard: [
          [
            {
              text: keepEnglishText,
              callback_data: `lang_keep_en`,
            },
          ],
          [
            {
              text: switchTexts[detectedLanguage as keyof typeof switchTexts],
              callback_data: `lang_switch_${detectedLanguage}`,
            },
          ],
        ],
      },
    }
  );
}

export async function handleLanguageSwitch(ctx: Context): Promise<void> {
  try {
    const callbackData =
      ctx.callbackQuery && "data" in ctx.callbackQuery
        ? ctx.callbackQuery.data
        : "";
    const tgId = ctx.from?.id?.toString();

    if (!tgId || !callbackData) {
      return;
    }

    if (callbackData === "lang_keep_en") {
      // Keep English (default) - no need to store in database for optimization
      // This will delete any existing record if user previously had a different language
      await LanguagePreferenceService.setUserLanguage(tgId, SupportedLocale.EN);
      await ctx.editMessageText(
        T(ctx, botMessages.languageSetToEnglish.id, { APP_NAME })
      );
    } else if (callbackData.startsWith("lang_switch_")) {
      const language = callbackData.replace(
        "lang_switch_",
        ""
      ) as SupportedLocale;
      await LanguagePreferenceService.setUserLanguage(tgId, language);

      const confirmationMessages = {
        [SupportedLocale.UA]: TNoContext(
          botMessages.languageSetToUkrainian.id,
          { APP_NAME },
          SupportedLocale.UA
        ),
        [SupportedLocale.RU]: TNoContext(
          botMessages.languageSetToRussian.id,
          { APP_NAME },
          SupportedLocale.RU
        ),
      };

      await ctx.editMessageText(
        confirmationMessages[language as keyof typeof confirmationMessages] ||
          "✅ Language updated!"
      );
    }

    // Answer callback query
    await ctx.answerCbQuery();
  } catch (error) {
    console.error("Language switch error", error);
    await ctx.answerCbQuery("Error updating language preference");
  }
}
