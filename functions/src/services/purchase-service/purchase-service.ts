import * as admin from "firebase-admin";
import { OrderStatus, TxType, UserType } from "../../marketplace-shared";
import { refundProposals } from "../../proposal-functions/proposal-service";
import { TRANSACTION_DESCRIPTION_INTL_KEYS } from "../../transaction-description-intl-keys";
import { lockFundsWithHistory } from "../balance-service/balance-service";
import { DBOrdersCollection } from "../db.service";
import { addDeadlineIfMarketCollection as addDeadlineIfCollectionIsLaunched } from "../deadline-service/deadline-service";
import {
  notifySellerOrderPaid,
  sendNotificationSafely,
} from "../notification-service";
import {
  validateBuyerPurchase,
  validateSellerPurchase,
} from "../order-service/order-validation-service";
import {
  processFeesAndTransferToSeller,
  processFeesOnPurchase,
} from "./purchase-fee-processing-service";

export async function processPurchase(params: {
  userId: string;
  orderId: string;
  userType: UserType;
}) {
  const { userId, orderId, userType } = params;

  await refundProposals(orderId);

  const validation =
    userType === UserType.BUYER
      ? await validateBuyerPurchase({ userId, orderId })
      : await validateSellerPurchase({ userId, orderId });

  const { order, lockedAmount, lockPercentage } = validation;

  // Helper function to create default fee result
  const createDefaultFeeResult = () => ({
    totalFee: 0,
    referralFee: 0,
    marketplaceFee: 0,
    netAmountToSeller: 0,
  });

  // Apply purchase fees if this is a buyer purchase
  let feeResult = createDefaultFeeResult();
  if (userType === UserType.BUYER) {
    // Process fees and conditionally transfer to seller if gift exists
    const processingFunction = order.giftId
      ? processFeesAndTransferToSeller
      : processFeesOnPurchase;

    feeResult = await processingFunction(order, userId);
  }

  // Lock funds for the user (skip for buyers when order has gift since funds are already transferred)
  const shouldLockFunds = !(userType === UserType.BUYER && order.giftId);

  if (shouldLockFunds) {
    const txType =
      userType === UserType.BUYER
        ? TxType.BUY_LOCK_COLLATERAL
        : TxType.SELL_LOCK_COLLATERAL;
    await lockFundsWithHistory({
      userId,
      amount: lockedAmount - feeResult.totalFee,
      txType,
      orderId,
      descriptionIntlKey:
        userType === UserType.BUYER
          ? TRANSACTION_DESCRIPTION_INTL_KEYS.LOCKED_COLLATERAL_FOR_BUYER
          : TRANSACTION_DESCRIPTION_INTL_KEYS.LOCKED_COLLATERAL_FOR_SELLER,
      descriptionIntlParams: {
        amount: lockedAmount.toString(),
        percentage: (lockPercentage * 100).toString(),
        orderPrice: order.price.toString(),
      },
    });
  }

  // Prepare update data
  // If buyer is purchasing an order that already has gift or giftId,
  // set status directly to GIFT_SENT_TO_RELAYER
  const shouldSkipToPaidStatus = userType === UserType.BUYER && order.giftId;

  const updateData: any = {
    status: shouldSkipToPaidStatus
      ? OrderStatus.GIFT_SENT_TO_RELAYER
      : OrderStatus.PAID,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  };

  // Set buyer or seller ID based on user type
  if (userType === UserType.BUYER) {
    updateData.buyerId = userId;
  } else {
    updateData.sellerId = userId;
  }

  // Add deadline if collection is in MARKET status
  await addDeadlineIfCollectionIsLaunched(
    order.collectionId,
    orderId,
    updateData
  );

  // Update order
  await DBOrdersCollection.doc(orderId).update(updateData);

  // Send notification to seller when buyer purchases (order status becomes PAID)
  if (userType === UserType.BUYER && updateData.status === OrderStatus.PAID) {
    sendNotificationSafely(
      notifySellerOrderPaid,
      {
        orderId,
        sellerId: order.sellerId!,
        orderNumber: order.number,
        price: order.price,
      },
      "purchase-flow-service"
    );
  }

  const actionMessage =
    userType === UserType.BUYER
      ? shouldSkipToPaidStatus
        ? "Gift is ready to be claimed from relayer."
        : "Waiting for seller to send gift."
      : "You can now send the gift.";

  const feeMessage =
    feeResult.totalFee > 0
      ? ` Purchase fee of ${feeResult.totalFee} TON applied.`
      : "";

  // Different message for orders with gifts where money is already transferred
  const lockMessage = shouldLockFunds
    ? `${lockedAmount} TON locked (${lockPercentage * 100}% of ${
        order.price
      } TON order).`
    : `Payment completed! ${feeResult.netAmountToSeller} TON transferred to seller.`;

  return {
    success: true,
    message: `Purchase successful! ${lockMessage}${feeMessage} ${actionMessage}`,
    lockedAmount: shouldLockFunds ? lockedAmount : 0,
    orderAmount: order.price,
    lockPercentage: lockPercentage * 100,
    feeApplied: feeResult.totalFee,
    referralFee: feeResult.referralFee,
    marketplaceFee: feeResult.marketplaceFee,
    netAmountToSeller: feeResult.netAmountToSeller,
  };
}
