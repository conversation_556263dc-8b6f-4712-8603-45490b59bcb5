import { HttpsError } from "firebase-functions/v2/https";

export function throwUnauthenticated(): never {
  throw new HttpsError("unauthenticated", "User must be authenticated.");
}

export function throwInvalidOrderId(): never {
  throw new HttpsError("invalid-argument", "Valid order ID is required.");
}

export function throwInvalidSecondaryPrice(): never {
  throw new HttpsError(
    "invalid-argument",
    "Valid secondary market price is required."
  );
}

export function throwOrderNotFound(): never {
  throw new HttpsError("not-found", "Order not found.");
}

export function throwInvalidOrderStatus(): never {
  throw new HttpsError(
    "failed-precondition",
    "Only orders with PAID status can be listed on secondary market."
  );
}

export function throwNotOrderBuyer(): never {
  throw new HttpsError(
    "permission-denied",
    "Only the current buyer can set secondary market price."
  );
}

export function throwMissingParticipants(): never {
  throw new HttpsError(
    "failed-precondition",
    "Order must have both buyer and seller to be listed on secondary market."
  );
}

export function throwPriceBelowMinimum(minPrice: number): never {
  throw new HttpsError(
    "invalid-argument",
    `Secondary market price must be at least ${minPrice} TON.`
  );
}

export function throwPriceExceedsCollateral(
  totalCollateral: number,
  buyerAmount: number,
  sellerAmount: number
): never {
  throw new HttpsError(
    "invalid-argument",
    `Secondary market price cannot exceed total collateral of ${totalCollateral.toFixed(
      2
    )} TON (buyer: ${buyerAmount.toFixed(
      2
    )} TON + seller: ${sellerAmount.toFixed(2)} TON).`
  );
}

export function throwNotAvailableOnSecondaryMarket(): never {
  throw new HttpsError(
    "failed-precondition",
    "Order is not available on secondary market."
  );
}

export function throwInvalidSecondaryOrderStatus(): never {
  throw new HttpsError(
    "failed-precondition",
    "Only orders with PAID status can be purchased on secondary market."
  );
}

export function throwSellerCannotPurchase(): never {
  throw new HttpsError(
    "permission-denied",
    "Seller cannot purchase their own order on secondary market."
  );
}

export function throwBuyerCannotPurchase(): never {
  throw new HttpsError(
    "permission-denied",
    "Current buyer cannot purchase their own order on secondary market."
  );
}

export function throwNewBuyerNotFound(): never {
  throw new HttpsError("not-found", "New buyer not found.");
}

export function throwSecondaryMarketInternalError(message?: string): never {
  throw new HttpsError(
    "internal",
    message ?? "Server error in secondary market operation."
  );
}
