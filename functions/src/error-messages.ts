export interface ErrorMessageParams {
  [key: string]: string | number;
}

export interface ErrorResponse {
  errorKey: string;
  params?: ErrorMessageParams;
  fallbackMessage?: string;
}
// TODO move in core package
export const AUTH_ERRORS = {
  UNAUTHENTICATED: "errors.auth.unauthenticated",
  PERMISSION_DENIED: "errors.auth.permissionDenied",
  PERMISSION_DENIED_WITH_OPERATION: "errors.auth.permissionDeniedWithOperation",
  ADMIN_ONLY: "errors.auth.adminOnly",
  USER_NOT_FOUND: "errors.auth.userNotFound",
  TON_WALLET_REQUIRED: "errors.auth.tonWalletRequired",
  WALLET_ALREADY_USED: "errors.auth.walletAlreadyUsed",
  TELEGRAM_ID_REQUIRED: "errors.auth.telegramIdRequired",
} as const;

export const VALIDATION_ERRORS = {
  REQUIRED_FIELD: "errors.validation.requiredField",
  POSITIVE_AMOUNT_REQUIRED: "errors.validation.positiveAmountRequired",
  INVALID_ORDER_ID: "errors.validation.invalidOrderId",
  INVALID_COLLECTION_ID: "errors.validation.invalidCollectionId",
  INVALID_PRICE: "errors.validation.invalidPrice",
  INVALID_SECONDARY_MARKET_PRICE:
    "errors.validation.invalidSecondaryMarketPrice",
  INVALID_USER_ID: "errors.validation.invalidUserId",
  GIFT_REQUIRED: "errors.validation.ownedGiftIdRequired",
  USER_ID_OR_TG_ID_REQUIRED: "errors.validation.userIdOrTgIdRequired",
  INVALID_GIFT_ID: "errors.validation.invalidGiftId",
} as const;

export const GIFT_ERRORS = {
  GIFT_NOT_FOUND: "errors.gift.giftNotFound",
  GIFT_INVALID_STATUS: "errors.gift.giftInvalidStatus",
  GIFT_ALREADY_LINKED: "errors.gift.giftAlreadyLinked",
  GIFT_NOT_OWNED_BY_USER: "errors.gift.giftNotOwnedByUser",
  GIFT_COLLECTION_MISMATCH: "errors.gift.giftCollectionMismatch",
} as const;

export const ORDER_ERRORS = {
  ORDER_NOT_FOUND: "errors.order.orderNotFound",
  INSUFFICIENT_BALANCE: "errors.order.insufficientBalance",
  COLLECTION_NOT_FOUND: "errors.order.collectionNotFound",
  COLLECTION_NOT_ACTIVE: "errors.order.collectionNotActive",
  INVALID_ORDER_STATUS: "errors.order.invalidOrderStatus",
  ONLY_PAID_ORDERS_SECONDARY_MARKET:
    "errors.order.onlyPaidOrdersSecondaryMarket",
  ONLY_BUYER_CAN_SET_SECONDARY_PRICE:
    "errors.order.onlyBuyerCanSetSecondaryPrice",
  ORDER_MUST_HAVE_BUYER_AND_SELLER: "errors.order.orderMustHaveBuyerAndSeller",
  SECONDARY_PRICE_EXCEEDS_COLLATERAL:
    "errors.order.secondaryPriceExceedsCollateral",
  ORDER_NOT_AVAILABLE_SECONDARY_MARKET:
    "errors.order.orderNotAvailableSecondaryMarket",
  TOO_MANY_CREATED_ORDERS: "errors.order.tooManyCreatedOrders",
  ONLY_PAID_ORDERS_PURCHASABLE: "errors.order.onlyPaidOrdersPurchasable",
  SELLER_CANNOT_PURCHASE_OWN_ORDER: "errors.order.sellerCannotPurchaseOwnOrder",
  BUYER_CANNOT_PURCHASE_SAME_ORDER: "errors.order.buyerCannotPurchaseSameOrder",
  SECONDARY_PRICE_BELOW_MINIMUM: "errors.order.secondaryPriceBelowMinimum",
  ORDER_MUST_BE_PAID_STATUS: "errors.order.orderMustBePaidStatus",
  ORDER_MUST_BE_GIFT_SENT_STATUS: "errors.order.orderMustBeGiftSentStatus",
  BUYERS_CANNOT_CREATE_MARKET_ORDERS:
    "errors.order.buyersCannotCreateMarketOrders",
  INVALID_COLLECTION_STATUS: "errors.order.invalidCollectionStatus",
  AMOUNT_BELOW_FLOOR_PRICE: "errors.order.amountBelowFloorPrice",
  ORDER_NOT_OWNED_BY_USER: "errors.order.orderNotOwnedByUser",
  CANNOT_PURCHASE_OWN_ORDER: "errors.order.cannotPurchaseOwnOrder",
  NOT_AVAILABLE_ON_SECONDARY_MARKET:
    "errors.order.notAvailableOnSecondaryMarket",
  SELLER_CANNOT_PURCHASE: "errors.order.sellerCannotPurchase",
  BUYER_ALREADY_OWNS: "errors.order.buyerAlreadyOwns",
  MISSING_PARTICIPANTS: "errors.order.missingParticipants",
} as const;

export const FULFILL_AND_RESELL_ERRORS = {
  INVALID_PARAMETERS: "errors.fulfillAndResell.invalidParameters",
  ORDER_NOT_FOUND: "errors.fulfillAndResell.orderNotFound",
  NOT_ORDER_BUYER: "errors.fulfillAndResell.notOrderBuyer",
  INVALID_ORDER_STATUS: "errors.fulfillAndResell.invalidOrderStatus",
  INSUFFICIENT_BALANCE: "errors.fulfillAndResell.insufficientBalance",
} as const;

export const WITHDRAWAL_ERRORS = {
  AMOUNT_BELOW_MINIMUM: "errors.withdrawal.amountBelowMinimum",
  AMOUNT_ABOVE_MAXIMUM: "errors.withdrawal.amountAboveMaximum",
  AMOUNT_EXCEEDS_24H_LIMIT: "errors.withdrawal.amountExceeds24hLimit",
  INSUFFICIENT_AVAILABLE_BALANCE:
    "errors.withdrawal.insufficientAvailableBalance",
  AMOUNT_TOO_SMALL_AFTER_FEES: "errors.withdrawal.amountTooSmallAfterFees",
} as const;

export const TELEGRAM_ERRORS = {
  INIT_DATA_REQUIRED: "errors.telegram.initDataRequired",
  BOT_TOKEN_NOT_CONFIGURED: "errors.telegram.botTokenNotConfigured",
  INVALID_TELEGRAM_DATA: "errors.telegram.invalidTelegramData",
  FIREBASE_AUTH_ERROR: "errors.telegram.firebaseAuthError",
  IAM_PERMISSION_ERROR: "errors.telegram.iamPermissionError",
} as const;

export const PROPOSAL_ERRORS = {
  ORDER_NOT_FOUND: "errors.proposal.orderNotFound",
  PROPOSAL_ONLY_ON_SELL_ORDERS: "errors.proposal.proposalOnlyOnSellOrders",
  ORDER_MUST_BE_ACTIVE: "errors.proposal.orderMustBeActive",
  SELLER_CANNOT_PROPOSE: "errors.proposal.sellerCannotPropose",
  CANNOT_PROPOSE_ON_SECONDARY_MARKET:
    "errors.proposal.cannotProposeOnSecondaryMarket",
  INVALID_PROPOSED_PRICE: "errors.proposal.invalidProposedPrice",
  USER_ALREADY_HAS_ACTIVE_PROPOSAL:
    "errors.proposal.userAlreadyHasActiveProposal",
  INSUFFICIENT_BALANCE: "errors.proposal.insufficientBalance",
  NO_ACTIVE_PROPOSAL_FOUND: "errors.proposal.noActiveProposalFound",
  PROPOSAL_NOT_FOUND: "errors.proposal.proposalNotFound",
  ONLY_SELLER_CAN_ACCEPT_PROPOSAL:
    "errors.proposal.onlySellerCanAcceptProposal",
  INTERNAL_ERROR: "errors.proposal.internalError",
} as const;

export const GENERIC_ERRORS = {
  SERVER_ERROR: "errors.generic.serverError",
  UNKNOWN_ERROR: "errors.generic.unknownError",
  OPERATION_FAILED: "errors.generic.operationFailed",
  AUTHENTICATION_FAILED: "errors.generic.authenticationFailed",
} as const;

export function createErrorResponse(
  errorKey: string,
  params?: ErrorMessageParams,
  fallbackMessage?: string
): ErrorResponse {
  return {
    errorKey,
    params,
    fallbackMessage,
  };
}

export function createInternationalizedError(
  code: string,
  errorKey: string,
  params?: ErrorMessageParams,
  fallbackMessage?: string
): Error {
  const errorResponse = createErrorResponse(errorKey, params, fallbackMessage);
  const error = new Error(JSON.stringify(errorResponse));
  (error as any).code = code;
  return error;
}
