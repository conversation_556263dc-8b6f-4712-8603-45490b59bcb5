import { GiftEntity, GiftStatus, OrderStatus } from "../../marketplace-shared";

import { validateTelegramId } from "../user-service/user.validator";
import { analyzeOrdersForGift } from "../shared/order-filtering.service";
import {
  getGiftsByOwnerAndStatus,
  getAllOrders,
} from "../shared/database.service";
import { GiftQueryLogger } from "./gift-query.logger";

export interface GiftWithOrderInfo extends GiftEntity {
  relatedOrder?: {
    id: string;
    number: number;
    status: string;
    price: number;
  } | null;
}

const OPERATION_NAME = "get_user_gifts_available_for_withdrawal_for_bot";

export async function getUserGiftsAvailableForWithdrawal(params: {
  tg_id: string;
}): Promise<GiftWithOrderInfo[]> {
  const { tg_id } = params;

  try {
    validateTelegramId(tg_id);

    GiftQueryLogger.logGiftQueryStart({ tg_id, operation: OPERATION_NAME });

    const gifts = await getGiftsByOwnerAndStatus(tg_id, GiftStatus.DEPOSITED);
    GiftQueryLogger.logGiftsFound({
      tg_id,
      totalGifts: gifts.length,
      operation: OPERATION_NAME,
    });

    const orders = await getAllOrders();

    const availableGifts: GiftWithOrderInfo[] = [];

    for (const gift of gifts) {
      if (!gift.id) continue;

      const orderAnalysis = await analyzeOrdersForGift(
        orders as any,
        gift.id,
        tg_id
      );

      if (orderAnalysis.hasActiveOrder) {
        GiftQueryLogger.logGiftHasActiveOrder({
          giftId: gift.id,
          operation: OPERATION_NAME,
        });
        continue;
      }

      if (orderAnalysis.hasWithdrawableOrder) {
        if (!orderAnalysis.relatedOrder) {
          GiftQueryLogger.logGiftNotLinked({
            giftId: gift.id,
            operation: OPERATION_NAME,
          });
        } else if (
          orderAnalysis.relatedOrder.status === OrderStatus.GIFT_SENT_TO_RELAYER
        ) {
          GiftQueryLogger.logGiftEligibleForBuyer({
            giftId: gift.id,
            orderId: orderAnalysis.relatedOrder.id,
            operation: OPERATION_NAME,
          });
        } else if (
          orderAnalysis.relatedOrder.status === OrderStatus.CANCELLED
        ) {
          GiftQueryLogger.logGiftEligibleForSeller({
            giftId: gift.id,
            orderId: orderAnalysis.relatedOrder.id,
            operation: OPERATION_NAME,
          });
        }

        availableGifts.push({
          ...gift,
          relatedOrder: orderAnalysis.relatedOrder || null,
        } as GiftWithOrderInfo);
      }
    }

    GiftQueryLogger.logQuerySuccess({
      tg_id,
      totalGifts: gifts.length,
      availableGifts: availableGifts.length,
      operation: OPERATION_NAME,
    });

    return availableGifts;
  } catch (error) {
    GiftQueryLogger.logQueryError(error, { tg_id, operation: OPERATION_NAME });
    throw error;
  }
}
