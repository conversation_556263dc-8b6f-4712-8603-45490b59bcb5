import * as admin from "firebase-admin";
import { TRANSACTION_DESCRIPTION_INTL_KEYS } from "../../transaction-description-intl-keys";
import {
  OrderEntity,
  OrderStatus,
  Role,
  TxType,
  UserType,
} from "../../marketplace-shared";
import { refundProposals } from "../../proposal-functions/proposal-service";
import { bpsToDecimal, safeMultiply, safeSubtract } from "../../utils";
import {
  addFundsWithHistory,
  spendLockedFunds,
  unlockFundsWithHistory,
} from "../balance-service/balance-service";
import { DBOrdersCollection, DBUserCollection } from "../db.service";
import {
  applyFeeToMarketplaceRevenue,
  applyFixedCancelOrderFee,
} from "../fee-service/fee-service";
import { createTransactionRecord } from "../transaction-history-service/transaction-history-service";

// Types for cancellation processing

interface PenaltyProcessingParams {
  userId: string;
  amount: number;
  orderId: string;
  descriptionKey: string;
  isReceivingCompensation?: boolean;
}

interface UnlockProcessingParams {
  userId: string;
  amount: number;
  orderId: string;
  descriptionKey: string;
}

// Helper function to calculate collateral amounts
function calculateCollateralAmounts(order: OrderEntity) {
  const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage ?? 0;
  const sellerLockPercentageBPS = order.fees?.seller_locked_percentage ?? 0;
  const purchaseFeeBPS = order.fees?.purchase_fee ?? 0;

  const buyerLockPercentage = bpsToDecimal(buyerLockPercentageBPS);
  const sellerLockPercentage = bpsToDecimal(sellerLockPercentageBPS);

  const totalFeeAmount =
    purchaseFeeBPS > 0
      ? safeMultiply(order.price, bpsToDecimal(purchaseFeeBPS))
      : 0;

  const buyerLockedAmount =
    safeMultiply(order.price, buyerLockPercentage) - totalFeeAmount;
  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);

  return { buyerLockedAmount, sellerLockedAmount, totalFeeAmount };
}

// Helper function to process penalty (spend locked funds + create transaction)
async function processPenalty(params: PenaltyProcessingParams) {
  const {
    userId,
    amount,
    orderId,
    descriptionKey,
    isReceivingCompensation = false,
  } = params;

  await spendLockedFunds(userId, amount);
  await createTransactionRecord({
    userId,
    txType: TxType.CANCELATION_FEE,
    amount,
    orderId,
    descriptionIntlKey: descriptionKey,
    descriptionIntlParams: { amount: amount.toString() },
    isReceivingCompensation,
  });
}

// Helper function to process unlock with history
async function processUnlock(params: UnlockProcessingParams): Promise<void> {
  const { userId, amount, orderId, descriptionKey } = params;

  await unlockFundsWithHistory({
    userId,
    amount,
    txType: TxType.UNLOCK_COLLATERAL,
    orderId,
    descriptionIntlKey: descriptionKey,
    descriptionIntlParams: { amount: amount.toString() },
  });
}

// Helper function to update order status
async function updateOrderStatus(
  orderId: string,
  status: OrderStatus
): Promise<void> {
  await DBOrdersCollection.doc(orderId).update({
    status,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });
}

async function getAdminUserIds(userIds: string[]) {
  if (userIds.length === 0) return new Set();

  const userDocs = await Promise.all(
    userIds.map((id) => DBUserCollection.doc(id).get())
  );

  const adminIds = new Set<string>();
  userDocs.forEach((doc, index) => {
    if (doc.exists && doc.data()?.role === Role.ADMIN) {
      adminIds.add(userIds[index]);
    }
  });

  return adminIds;
}

async function processSellerCancellation(params: {
  order: OrderEntity;
  sellerLockedAmount: number;
  buyerLockedAmount: number;
}) {
  const { order, sellerLockedAmount, buyerLockedAmount } = params;

  // Parallelize seller penalty and buyer unlock operations
  await Promise.all([
    processPenalty({
      userId: order.sellerId!,
      amount: sellerLockedAmount,
      orderId: order.id!,
      descriptionKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.CANCELLATION_PENALTY_FOR_SELLER,
      isReceivingCompensation: false,
    }),
    processUnlock({
      userId: order.buyerId!,
      amount: buyerLockedAmount,
      orderId: order.id!,
      descriptionKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.COLLATERAL_UNLOCKED_DUE_TO_SELLER_CANCELLATION,
    }),
  ]);
}

async function processBuyerCancellation(params: {
  order: OrderEntity;
  buyerLockedAmount: number;
  sellerLockedAmount: number;
  remainingBuyerCollateral: number;
}) {
  const {
    order,
    buyerLockedAmount,
    sellerLockedAmount,
    remainingBuyerCollateral,
  } = params;

  // Parallelize buyer penalty and seller compensation operations
  await Promise.all([
    processPenalty({
      userId: order.buyerId!,
      amount: buyerLockedAmount,
      orderId: order.id!,
      descriptionKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.CANCELLATION_PENALTY_FOR_BUYER,
      isReceivingCompensation: false,
    }),
    addFundsWithHistory({
      userId: order.sellerId!,
      amount: remainingBuyerCollateral,
      txType: TxType.CANCELATION_FEE,
      orderId: order.id,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.CANCELLATION_COMPENSATION_FROM_BUYER_COLLATERAL,
      descriptionIntlParams: {
        amount: remainingBuyerCollateral.toString(),
      },
      isReceivingCompensation: true,
    }),
    processUnlock({
      userId: order.sellerId!,
      amount: sellerLockedAmount,
      orderId: order.id!,
      descriptionKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.COLLATERAL_UNLOCKED_DUE_TO_BUYER_CANCELLATION,
    }),
  ]);
}

async function processAdminOrderCancellation(order: OrderEntity) {
  const hasBothParticipants = Boolean(order.buyerId && order.sellerId);
  const isActiveSinglePersonOrder =
    order.status === OrderStatus.ACTIVE && !hasBothParticipants;
  const isPaidTwoPersonOrder =
    order.status === OrderStatus.PAID && hasBothParticipants;

  if (isActiveSinglePersonOrder) {
    return processAdminSinglePersonCancellation(order);
  } else if (isPaidTwoPersonOrder) {
    return processAdminTwoPersonCancellation(order);
  } else {
    throw new Error(
      `Order ${order.id} is not in a valid state for admin cancellation`
    );
  }
}

async function processAdminSinglePersonCancellation(order: OrderEntity) {
  await updateOrderStatus(order.id!, OrderStatus.CANCELLED);

  return {
    success: true,
    message:
      "Order cancelled by admin. All locked collateral has been released without penalties.",
    feeApplied: 0,
    feeType: "none",
  };
}

async function processAdminTwoPersonCancellation(order: OrderEntity) {
  const { buyerLockedAmount, sellerLockedAmount } =
    calculateCollateralAmounts(order);

  const userIds = [order.buyerId, order.sellerId].filter(Boolean) as string[];
  const adminUserIds = await getAdminUserIds(userIds);

  // Unlock collateral for both parties without any penalties
  // Skip unlocking funds for admin users
  const unlockOperations = [];

  if (order.buyerId && !adminUserIds.has(order.buyerId)) {
    unlockOperations.push(
      processUnlock({
        userId: order.buyerId,
        amount: buyerLockedAmount,
        orderId: order.id!,
        descriptionKey:
          TRANSACTION_DESCRIPTION_INTL_KEYS.COLLATERAL_UNLOCKED_DUE_TO_ADMIN_CANCELLATION,
      })
    );
  }

  if (order.sellerId && !adminUserIds.has(order.sellerId)) {
    unlockOperations.push(
      processUnlock({
        userId: order.sellerId,
        amount: sellerLockedAmount,
        orderId: order.id!,
        descriptionKey:
          TRANSACTION_DESCRIPTION_INTL_KEYS.COLLATERAL_UNLOCKED_DUE_TO_ADMIN_CANCELLATION,
      })
    );
  }

  // Parallelize unlock operations, proposal refunding, and order status update
  await Promise.all([
    ...unlockOperations,
    refundProposals(order.id!),
    updateOrderStatus(order.id!, OrderStatus.CANCELLED),
  ]);

  return {
    success: true,
    message:
      "Order cancelled by admin. All locked collateral has been released without penalties.",
    feeApplied: 0,
    feeType: "none",
  };
}

export async function processOrderCancellation(
  order: OrderEntity,
  cancellingUserId: string
) {
  const cancellingUserDoc = await DBUserCollection.doc(cancellingUserId).get();
  const cancellingUser = cancellingUserDoc.exists
    ? cancellingUserDoc.data()
    : null;
  const isAdminCancelling = cancellingUser?.role === Role.ADMIN;

  if (isAdminCancelling) {
    return processAdminOrderCancellation(order);
  }

  // Route to regular user cancellation functions
  const hasBothParticipants = Boolean(order.buyerId && order.sellerId);
  const isActiveSinglePersonOrder =
    order.status === OrderStatus.ACTIVE && !hasBothParticipants;
  const isPaidTwoPersonOrder =
    order.status === OrderStatus.PAID && hasBothParticipants;
  const isActiveOrderWithGift =
    order.status === OrderStatus.ACTIVE && order.giftId && hasBothParticipants;

  if (isActiveSinglePersonOrder) {
    return processUserSinglePersonCancellation(order, cancellingUserId);
  } else if (isPaidTwoPersonOrder) {
    return processUserTwoPersonCancellation(order, cancellingUserId);
  } else if (isActiveOrderWithGift) {
    return processActiveOrderWithGiftCancellation(order);
  } else {
    throw new Error(
      `Order ${order.id} is not in a valid state for cancellation`
    );
  }
}

async function processUserSinglePersonCancellation(
  order: OrderEntity,
  cancellingUserId: string
) {
  const { buyerLockedAmount, sellerLockedAmount } =
    calculateCollateralAmounts(order);

  // Unlock collateral for the cancelling user
  if (order.buyerId === cancellingUserId) {
    await processUnlock({
      userId: order.buyerId,
      amount: buyerLockedAmount,
      orderId: order.id!,
      descriptionKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.COLLATERAL_UNLOCKED_DUE_TO_CANCELLATION,
    });
  } else if (order.sellerId === cancellingUserId) {
    await processUnlock({
      userId: order.sellerId,
      amount: sellerLockedAmount,
      orderId: order.id!,
      descriptionKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.COLLATERAL_UNLOCKED_DUE_TO_CANCELLATION,
    });
  }

  // Parallelize fee application, proposal refunding, and order status update
  const [feeApplied] = await Promise.all([
    applyFixedCancelOrderFee(cancellingUserId),
    refundProposals(order.id!),
    updateOrderStatus(order.id!, OrderStatus.CANCELLED),
  ]);

  const feeMessage =
    feeApplied > 0
      ? ` A cancellation fee of ${feeApplied} TON was applied.`
      : "";

  return {
    success: true,
    message: `Order cancelled successfully. Locked funds have been released.${feeMessage}`,
    feeApplied,
    feeType: "fixed",
  };
}

async function processUserTwoPersonCancellation(
  order: OrderEntity,
  cancellingUserId: string
) {
  const cancelFeePercentageBPS = order.fees?.order_cancellation_fee ?? 0;
  const cancelFeePercentage = bpsToDecimal(cancelFeePercentageBPS);

  const { buyerLockedAmount, sellerLockedAmount } =
    calculateCollateralAmounts(order);

  // Calculate marketplace fee from buyer's collateral (not full order price)
  const marketplaceFee = safeMultiply(buyerLockedAmount, cancelFeePercentage);
  const remainingBuyerCollateral = safeSubtract(
    buyerLockedAmount,
    marketplaceFee
  );

  // Determine who is cancelling and apply appropriate logic
  if (cancellingUserId === order.sellerId) {
    await processSellerCancellation({
      order,
      sellerLockedAmount,
      buyerLockedAmount,
    });
  } else if (cancellingUserId === order.buyerId) {
    await processBuyerCancellation({
      order,
      buyerLockedAmount,
      sellerLockedAmount,
      remainingBuyerCollateral,
    });
  }

  // Parallelize fee operations and proposal refunding
  const feeOperations = [];

  // Apply marketplace fee
  if (marketplaceFee > 0) {
    feeOperations.push(
      applyFeeToMarketplaceRevenue({
        feeAmount: marketplaceFee,
        feeType: "cancel_order_penalty",
      })
    );
  }

  // Handle reseller earnings for seller
  const resellerEarnings = order.reseller_earnings_for_seller ?? 0;
  if (resellerEarnings > 0) {
    if (cancellingUserId === order.sellerId) {
      // Seller cancels: reseller earnings go to marketplace revenue
      feeOperations.push(
        applyFeeToMarketplaceRevenue({
          feeAmount: resellerEarnings,
          feeType: "reseller_earnings_seller_cancel",
        })
      );
    } else if (cancellingUserId === order.buyerId) {
      // Buyer cancels: reseller earnings go to seller
      feeOperations.push(
        addFundsWithHistory({
          userId: order.sellerId!,
          amount: resellerEarnings,
          txType: TxType.RESELL_FEE_EARNINGS,
          orderId: order.id,
          description: `Resell fee earnings from buyer cancellation (${resellerEarnings} TON)`,
        })
      );
    }
  }

  // Parallelize fee operations, proposal refunding, and order status update
  await Promise.all([
    ...feeOperations,
    refundProposals(order.id!),
    updateOrderStatus(order.id!, OrderStatus.CANCELLED),
  ]);

  const cancellerRole =
    cancellingUserId === order.sellerId ? UserType.SELLER : UserType.BUYER;
  const compensatedRole =
    cancellingUserId === order.sellerId ? UserType.BUYER : UserType.SELLER;

  const compensationAmount =
    cancellingUserId === order.sellerId
      ? buyerLockedAmount // Seller cancelled, buyer gets full collateral back
      : remainingBuyerCollateral; // Buyer cancelled, seller gets remaining collateral after fees

  return {
    success: true,
    message: `Order cancelled by ${cancellerRole}. ${compensatedRole} received ${compensationAmount} TON compensation. Marketplace fee: ${marketplaceFee} TON.`,
    feeApplied: marketplaceFee,
    feeType: "dynamic",
  };
}

async function processActiveOrderWithGiftCancellation(order: OrderEntity) {
  if (!order.id) {
    throw new Error("Order ID is required for cancellation");
  }

  // Unlock buyer collateral (if any) without fees
  if (order.buyerId) {
    const { buyerLockedAmount } = calculateCollateralAmounts(order);

    if (buyerLockedAmount > 0) {
      await Promise.all([
        updateOrderStatus(order.id, OrderStatus.CANCELLED),
        unlockFundsWithHistory({
          userId: order.buyerId,
          amount: buyerLockedAmount,
          txType: TxType.UNLOCK_COLLATERAL,
          orderId: order.id,
          descriptionIntlKey:
            TRANSACTION_DESCRIPTION_INTL_KEYS.UNLOCKED_BUYER_COLLATERAL_FOR_CANCELLED_ORDER,
          descriptionIntlParams: {
            orderNumber: order.number.toString(),
            amount: buyerLockedAmount.toString(),
          },
        }),
      ]);
    } else {
      await updateOrderStatus(order.id, OrderStatus.CANCELLED);
    }
  } else {
    await updateOrderStatus(order.id, OrderStatus.CANCELLED);
  }

  return {
    success: true,
    message:
      "Order cancelled successfully. No fees applied since gift is in relayer.",
    orderNumber: order.number,
    refundedAmount: 0, // No seller refund needed
    feeApplied: 0, // No cancellation fees
    feeType: "none",
  };
}
