import { OrderEntity, OrderStatus } from "../../marketplace-shared";
import { getUserTgIdByUserId } from "../user-service/user.service";

type OrderWithId = OrderEntity & { id: string };

const ACTIVE_ORDER_STATUSES = [
  OrderStatus.ACTIVE,
  OrderStatus.PAID,
  OrderStatus.CREATED,
];

const WITHDRAWABLE_BUYER_STATUSES = [OrderStatus.GIFT_SENT_TO_RELAYER];
const WITHDRAWABLE_SELLER_STATUSES = [OrderStatus.CANCELLED];

export async function analyzeOrdersForGift(
  orders: OrderWithId[],
  giftId: string,
  userTgId: string
) {
  const relatedOrders = orders.filter((order) => order.giftId === giftId);

  if (relatedOrders.length === 0) {
    return {
      hasActiveOrder: false,
      hasWithdrawableOrder: true,
      relatedOrder: null,
    };
  }

  // Check for active orders that prevent withdrawal
  for (const order of relatedOrders) {
    if (ACTIVE_ORDER_STATUSES.includes(order.status) && order.sellerId) {
      const sellerTgId = await getUserTgIdByUserId(order.sellerId);
      if (sellerTgId === userTgId) {
        return {
          hasActiveOrder: true,
          hasWithdrawableOrder: false,
          relatedOrder: null,
        };
      }
    }
  }

  // Check for withdrawal-eligible orders
  for (const order of relatedOrders) {
    const orderInfo = {
      id: order.id,
      number: order.number,
      status: order.status,
      price: order.price,
    };

    // Check buyer eligibility
    if (WITHDRAWABLE_BUYER_STATUSES.includes(order.status) && order.buyerId) {
      const buyerTgId = await getUserTgIdByUserId(order.buyerId);
      if (buyerTgId === userTgId) {
        return {
          hasActiveOrder: false,
          hasWithdrawableOrder: true,
          relatedOrder: orderInfo,
        };
      }
    }

    // Check seller eligibility
    if (WITHDRAWABLE_SELLER_STATUSES.includes(order.status) && order.sellerId) {
      const sellerTgId = await getUserTgIdByUserId(order.sellerId);
      if (sellerTgId === userTgId) {
        return {
          hasActiveOrder: false,
          hasWithdrawableOrder: true,
          relatedOrder: orderInfo,
        };
      }
    }
  }

  return {
    hasActiveOrder: false,
    hasWithdrawableOrder: false,
    relatedOrder: null,
  };
}
