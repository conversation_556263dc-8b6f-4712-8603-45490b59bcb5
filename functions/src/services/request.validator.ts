import { HttpsError } from "firebase-functions/v2/https";
import { VALIDATION_ERRORS } from "../error-messages";
import { UserType } from "../marketplace-shared";

export function validateRequiredField<T>(
  value: T | undefined | null,
  fieldName: string
): asserts value is T {
  if (value === undefined || value === null || value === "") {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.REQUIRED_FIELD,
        params: { fieldName },
        fallbackMessage: `${fieldName} is required.`,
      })
    );
  }
}

export function validateOrderId(orderId: string): void {
  validateRequiredField(orderId, "orderId");

  if (typeof orderId !== "string" || orderId.trim().length === 0) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_ORDER_ID,
        fallbackMessage: "Valid order ID is required.",
      })
    );
  }
}

export function validateCollectionId(collectionId: string): void {
  validateRequiredField(collectionId, "collectionId");

  if (typeof collectionId !== "string" || collectionId.trim().length === 0) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_COLLECTION_ID,
        fallbackMessage: "Valid collection ID is required.",
      })
    );
  }
}

export function validateUserId(userId: string): void {
  validateRequiredField(userId, "userId");

  if (typeof userId !== "string" || userId.trim().length === 0) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_USER_ID,
        fallbackMessage: "Valid user ID is required.",
      })
    );
  }
}

export function validateGiftId(giftId: string): void {
  validateRequiredField(giftId, "giftId");

  if (typeof giftId !== "string" || giftId.trim().length === 0) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_GIFT_ID,
        fallbackMessage: "Valid gift ID is required.",
      })
    );
  }
}

export function validatePrice(price: number): void {
  validateRequiredField(price, "price");

  if (typeof price !== "number" || price <= 0) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.INVALID_PRICE,
        fallbackMessage: "Price must be a positive number.",
      })
    );
  }
}

export function validateUserType(userType: UserType): void {
  validateRequiredField(userType, "userType");

  if (!Object.values(UserType).includes(userType)) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.REQUIRED_FIELD,
        params: { fieldName: "userType" },
        fallbackMessage: "Valid user type is required.",
      })
    );
  }
}

export interface OrderCreationParams {
  userId: string;
  collectionId: string;
  price: number;
  userType: UserType;
}

export function validateOrderCreationParams(
  params: OrderCreationParams,
  userType: UserType
): void {
  validateUserId(params.userId);
  validateCollectionId(params.collectionId);
  validatePrice(params.price);
  validateUserType(userType);
}

export interface PurchaseParams {
  userId: string;
  orderId: string;
}

export function validatePurchaseParams(
  params: PurchaseParams,
  userType: UserType
): void {
  validateUserId(params.userId);
  validateOrderId(params.orderId);
  validateUserType(userType);
}

export function validateUserIdOrTelegramId(
  userId?: string,
  telegramId?: string
): void {
  if (!userId && !telegramId) {
    throw new HttpsError(
      "invalid-argument",
      JSON.stringify({
        errorKey: VALIDATION_ERRORS.USER_ID_OR_TG_ID_REQUIRED,
        fallbackMessage: "Either userId or telegramId is required.",
      })
    );
  }
}

// Configuration validation function moved from proposal-notification-service.ts
export function validateBotConfiguration(
  botAppUrl?: string,
  authToken?: string
): { isConfigured: boolean; botAppUrl?: string; authToken?: string } {
  if (!botAppUrl || !authToken) {
    return { isConfigured: false };
  }
  return { isConfigured: true, botAppUrl, authToken };
}
