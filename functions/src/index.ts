import * as admin from "firebase-admin";
import { logger } from "firebase-functions/v2";
import { getEnv } from "./config";

if (!admin.apps.length) {
  const config = getEnv();

  // For Firebase Functions, we need to explicitly set the service account
  // to ensure it has the necessary permissions for custom token creation
  const serviceAccount = config.firebase?.service_account_key;

  if (serviceAccount) {
    // Use service account key if provided
    admin.initializeApp({
      credential: admin.credential.cert(JSON.parse(serviceAccount)),
      projectId: config.app.project_id,
    });
    logger.info("Firebase initialized with service account key", {
      operation: "firebase_init",
      method: "service_account",
    });
  } else {
    // Use default credentials with explicit project ID
    admin.initializeApp({
      credential: admin.credential.applicationDefault(),
      projectId: config.app.project_id,
    });
    logger.info("Firebase initialized with application default credentials", {
      operation: "firebase_init",
      method: "application_default",
    });
  }
}

admin.firestore().settings({
  ignoreUndefinedProperties: true,
});

// Feature-based monitor functions
export { botHealthCheck } from "./bot-health-check-function";
export { expiredOrdersMonitor } from "./expired-orders-monitor";
export { limitedCollectionsMonitor } from "./limited-collections-monitor";
export { tonMonitor as tonTransactionMonitor } from "./ton-monitor-function";

// Feature-based utility functions
export { getTxLookup, updateTxLookup } from "./tx-lookup-function";

// Bot functions have been migrated to marketplace-bot repository
// and are no longer exported from marketplace-functions

export {
  createOrderAsBuyer,
  makePurchaseAsBuyer,
} from "./order-functions/buyer-order";

export {
  createOrderAsSeller,
  makePurchaseAsSeller,
} from "./order-functions/seller-order";

export { withdrawFunds } from "./withdraw-function";

export { withdrawRevenue } from "./revenue-function";

export { signInWithTelegram } from "./telegram-auth-function";

export { changeUserData } from "./user-profile-function";

// Order functions from order-functions-new
export { fulfillOrderAndCreateResellOrder } from "./order-functions/fulfill-and-resell";
export { cancelUserOrder } from "./order-functions/general-order";
export {
  makeSecondaryMarketPurchase,
  setSecondaryMarketPrice,
} from "./order-functions/secondary-market-function";

export {
  clearDeadlines as clearOrderDeadlines,
  recalculateDeadlines as recalculateOrderDeadlines,
} from "./admin-collection";

export { linkGiftToOrder } from "./order-functions/link-gift-to-order/link-gift-to-order.function";

export { createSellOrderFromGift } from "./order-functions/create-sell-order-from-gift/create-sell-order-from-gift.function";

export {
  acceptProposal,
  cancelProposal,
  proposeOrderPrice,
} from "./proposal-functions";
