import { HttpsError } from "firebase-functions/v2/https";
import { OrderEntity, OrderStatus } from "../../marketplace-shared";
import { getAppConfig } from "../../services/fee-service/fee-service";
import { bpsToDecimal, safeMultiply } from "../../utils";

// Input validation functions
export function validateOrderId(orderId: string): void {
  if (!orderId || typeof orderId !== "string" || orderId.trim() === "") {
    throw new HttpsError("invalid-argument", "Valid order ID is required.");
  }
}

export function validateSecondaryPrice(price: number): void {
  if (!price || typeof price !== "number" || price <= 0) {
    throw new HttpsError(
      "invalid-argument",
      "Valid secondary market price is required."
    );
  }
}

export function validateUserId(userId: string): void {
  if (!userId || typeof userId !== "string" || userId.trim() === "") {
    throw new HttpsError("invalid-argument", "Valid user ID is required.");
  }
}

// Order existence and status validation
export function validateOrderForSecondaryMarket(order: OrderEntity): void {
  if (order.status !== OrderStatus.PAID) {
    throw new HttpsError(
      "failed-precondition",
      "Only orders with PAID status can be listed on secondary market."
    );
  }

  if (!order.buyerId || !order.sellerId) {
    throw new HttpsError(
      "failed-precondition",
      "Order must have both buyer and seller to be listed on secondary market."
    );
  }
}

// Permission validation
export function validateBuyerPermission(
  order: OrderEntity,
  userId: string
): void {
  if (order.buyerId !== userId) {
    throw new HttpsError(
      "permission-denied",
      "Only the current buyer can set secondary market price."
    );
  }
}

// Price validation functions
export async function validateSecondaryMarketPrice(
  price: number,
  order: OrderEntity
): Promise<void> {
  const config = await getAppConfig();
  const minSecondaryMarketPrice = config?.min_secondary_market_price ?? 1;

  if (price < minSecondaryMarketPrice) {
    throw new HttpsError(
      "invalid-argument",
      `Secondary market price must be at least ${minSecondaryMarketPrice} TON.`
    );
  }

  const { totalCollateral, buyerLockedAmount, sellerLockedAmount } =
    calculateCollateralAmounts(order);

  if (price > totalCollateral) {
    throw new HttpsError(
      "invalid-argument",
      `Secondary market price cannot exceed total collateral of ${totalCollateral.toFixed(
        2
      )} TON (buyer: ${buyerLockedAmount.toFixed(
        2
      )} TON + seller: ${sellerLockedAmount.toFixed(2)} TON).`
    );
  }
}

// Purchase validation functions
export function validateSecondaryMarketPurchasePermissions(
  order: OrderEntity,
  newBuyerId: string
): void {
  if (newBuyerId === order.sellerId) {
    throw new HttpsError(
      "permission-denied",
      "Seller cannot purchase their own order on secondary market."
    );
  }

  if (newBuyerId === order.buyerId) {
    throw new HttpsError(
      "permission-denied",
      "Current buyer cannot purchase their own order on secondary market."
    );
  }
}

// Utility functions
export function calculateCollateralAmounts(order: OrderEntity): {
  totalCollateral: number;
  buyerLockedAmount: number;
  sellerLockedAmount: number;
} {
  const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage ?? 0;
  const sellerLockPercentageBPS = order.fees?.seller_locked_percentage ?? 0;
  const buyerLockPercentage = bpsToDecimal(buyerLockPercentageBPS);
  const sellerLockPercentage = bpsToDecimal(sellerLockPercentageBPS);

  const buyerLockedAmount = safeMultiply(order.price, buyerLockPercentage);
  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);
  const totalCollateral = buyerLockedAmount + sellerLockedAmount;

  return { totalCollateral, buyerLockedAmount, sellerLockedAmount };
}

// Composite validation functions
export async function validateSetSecondaryPriceRequest(
  orderId: string,
  secondaryMarketPrice: number,
  userId: string,
  order: OrderEntity
): Promise<void> {
  validateOrderId(orderId);
  validateSecondaryPrice(secondaryMarketPrice);
  validateUserId(userId);
  validateOrderForSecondaryMarket(order);
  validateBuyerPermission(order, userId);
  await validateSecondaryMarketPrice(secondaryMarketPrice, order);
}

export function validateSecondaryPurchaseRequest(
  orderId: string,
  newBuyerId: string,
  order: OrderEntity
): void {
  validateOrderId(orderId);
  validateUserId(newBuyerId);
  validateOrderForSecondaryMarket(order);
  validateSecondaryMarketPurchasePermissions(order, newBuyerId);
}

// Legacy error throwing functions (for backward compatibility)
export function throwOrderNotFound(): never {
  throw new HttpsError("not-found", "Order not found.");
}

export function throwNewBuyerNotFound(): never {
  throw new HttpsError("not-found", "New buyer not found.");
}

export function throwSecondaryMarketInternalError(message?: string): never {
  throw new HttpsError(
    "internal",
    message ?? "Server error in secondary market operation."
  );
}
