import {
  getUserData,
  requireAuthentication,
} from "../../services/auth-service/auth.service";
import {
  validateBuyerOwnership,
  requireTelegramId,
} from "../../services/auth-service/auth.validator";
import {
  validateCollectionId,
  validateOrderId,
  validateUserId,
  validatePrice,
} from "../../services/request.validator";

export async function validateCreateOrderRequest(
  request: any,
  params: {
    buyerId: string;
    collectionId: string;
    price: number;
  }
) {
  const authRequest = requireAuthentication(request);
  validateUserId(params.buyerId);
  validateCollectionId(params.collectionId);
  validatePrice(params.price);
  validateBuyerOwnership(authRequest, params.buyerId);

  const user = await getUserData(authRequest.auth.uid);

  requireTelegramId(user, "create orders");

  return { authRequest, user };
}

export async function validatePurchaseRequest(
  request: any,
  params: {
    buyerId: string;
    orderId: string;
  }
) {
  const authRequest = requireAuthentication(request);
  validateUserId(params.buyerId);
  validateOrderId(params.orderId);
  validateBuyerOwnership(authRequest, params.buyerId);

  // Validate user has telegram ID for purchase operations
  const user = await getUserData(authRequest.auth.uid);

  requireTelegramId(user, "make purchases");

  return { authRequest, user };
}
