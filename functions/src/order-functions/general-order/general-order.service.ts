import { DBOrdersCollection } from "../../services/db.service";
import { OrderEntity } from "../../marketplace-shared";
import { processOrderCancellation } from "../../services/order-service/order-cancellation-service";
import {
  throwCancellationFailed,
  throwOrderNotFound,
} from "./general-order.validator";
import { validateCancellationPermission } from "../../services/order-service/order-validation-service";

export interface CancelOrderParams {
  orderId: string;
  userId: string;
}

export interface CancelOrderResult {
  success: boolean;
  message: string;
  order: {
    id: string;
    number: number;
    status: string;
  };
  feeApplied: number;
  feeType: string;
}

export async function cancelOrder({
  orderId,
  userId,
}: CancelOrderParams): Promise<CancelOrderResult> {
  const orderDoc = await DBOrdersCollection.doc(orderId).get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  try {
    // Validate cancellation permission using the service
    await validateCancellationPermission(order, userId);

    // Process the cancellation using unified logic
    const result = await processOrderCancellation(order, userId);

    return {
      success: result.success,
      message: result.message,
      order: {
        id: order.id!,
        number: order.number,
        status: "cancelled",
      },
      // @ts-expect-error note
      feeApplied: result?.feeApplied ?? 0,
      feeType: result.feeType,
    };
  } catch (error) {
    if (error instanceof Error) {
      throwCancellationFailed(error.message);
    }
    throw error;
  }
}
