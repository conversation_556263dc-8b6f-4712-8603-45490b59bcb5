import { onCall } from "firebase-functions/v2/https";
import { requireAuthentication } from "../../services/auth-service/auth.service";
import { commonFunctionsConfig } from "../../constants";
import { cancelOrder } from "./general-order-function.service";
import {
  throwInvalidArguments,
  throwPermissionDenied,
  throwGeneralOrderInternalError,
} from "./general-order-function.validator";
import { GeneralOrderLogger } from "./general-order-function.logger";

export const cancelUserOrder = onCall<{
  orderId: string;
  userId: string;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, userId } = request.data;

  const authRequest = requireAuthentication(request);

  if (!orderId || !userId) {
    throwInvalidArguments();
  }

  if (authRequest?.auth?.uid !== userId) {
    throwPermissionDenied();
  }

  try {
    GeneralOrderLogger.logCancelOrderStarted({ orderId, userId });

    const result = await cancelOrder({ orderId, userId });

    GeneralOrderLogger.logCancelOrderSuccess({
      orderId,
      userId,
      feeApplied: result.feeApplied,
      feeType: result.feeType,
    });

    return result;
  } catch (error) {
    GeneralOrderLogger.logCancelOrderError({ error, orderId, userId });
    throwGeneralOrderInternalError((error as any).message);
  }
});
