import { onCall } from "firebase-functions/v2/https";
import { requireAuthentication } from "../services/auth-service/auth.service";
import { commonFunctionsConfig } from "../constants";
import {
  validateWithdrawalRequest,
  validateWithdrawalLimits,
  validateUserBalance,
  calculateWithdrawalAmounts,
  processWithdrawal,
} from "./withdraw-function.service";
import {
  logWithdrawalProcessed,
  logWithdrawalError,
} from "./withdraw-function.logger";
import { throwWithdrawInternalError } from "./withdraw-function.validator";

export const withdrawFunds = onCall<{
  amount: number;
}>(commonFunctionsConfig, async (request) => {
  const authRequest = requireAuthentication(request);
  const { amount } = request.data;

  try {
    const userId = authRequest.auth.uid;

    // Validate withdrawal request and get user data + config
    const { user, config } = await validateWithdrawalRequest(userId, amount);

    // Parallelize validation operations
    await Promise.all([
      validateWithdrawalLimits(userId, amount, config),
      validateUserBalance(userId, amount),
    ]);

    // Calculate withdrawal amounts
    const { feeAmount, netAmountToUser } = await calculateWithdrawalAmounts(
      userId,
      amount
    );

    // Process withdrawal
    const transferResult = await processWithdrawal(
      userId,
      amount,
      netAmountToUser,
      feeAmount,
      user
    );

    logWithdrawalProcessed({
      userId,
      netAmountToUser,
      feeAmount,
      walletAddress: user.ton_wallet_address!,
    });

    return {
      success: transferResult.success,
      message: `Withdrawal successful. ${netAmountToUser} TON sent to your wallet (${feeAmount} TON fee applied)`,
      netAmount: netAmountToUser,
      feeAmount,
      transactionHash: transferResult.transactionHash,
    };
  } catch (error) {
    logWithdrawalError({
      error,
      userId: request.auth?.uid,
      requestData: request.data,
    });

    throwWithdrawInternalError(error);
  }
});
